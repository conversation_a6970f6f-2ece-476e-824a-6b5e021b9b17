//
// RS485ProtocolHandlers.cpp - Protocol frame processing functions
// RS485 UMDF 2.0 Driver for AI-SLDAP Communication
//

#include "RS485FilterDriver.h"

//
// Route frame to appropriate API category handler
//
NTSTATUS RS485RouteFrameToAPICategory(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame)
{
    if (deviceContext == NULL || frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = STATUS_SUCCESS;

    // Extract command from payload (first 4 bytes)
    char command[5] = {0};
    RtlCopyMemory(command, frame->Payload, 4);

    RS485_DEBUG_PRINT("Processing frame with command: %s", command);

    // Route based on command prefix
    if (command[0] == 'S') {
        // System configuration commands (S001, S002, etc.)
        status = RS485ProcessSystemConfigFrame(deviceContext, frame);
    }
    else if (command[0] == 'U') {
        // User configuration commands (U001, U002, etc.)
        status = RS485ProcessUserConfigFrame(deviceContext, frame);
    }
    else if (command[0] == 'A') {
        // Data query commands (A001, A002, etc.)
        status = RS485ProcessDataQueryFrame(deviceContext, frame);
    }
    else if (command[0] == 'W') {
        // Model data commands (W001, W002, etc.)
        status = RS485ProcessModelDataFrame(deviceContext, frame);
    }
    else {
        RS485_DEBUG_PRINT("Unknown command prefix: %c", command[0]);
        status = STATUS_INVALID_PARAMETER;
        RS485HandleFrameError(deviceContext, frame, RS485_ERROR_INVALID_DATA_FORMAT);
    }

    return status;
}

//
// Process assign data frame (Master -> Slave configuration)
//
NTSTATUS RS485ProcessAssignDataFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame)
{
    if (deviceContext == NULL || frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    RS485_DEBUG_PRINT("Processing assign data frame");

    // Validate frame format
    if (frame->Header != RS485_FRAME_HEADER || frame->Trailer != RS485_FRAME_TRAILER) {
        RS485HandleFrameError(deviceContext, frame, RS485_ERROR_FRAME_ERROR);
        return STATUS_INVALID_PARAMETER;
    }

    // Route to appropriate handler based on command
    return RS485RouteFrameToAPICategory(deviceContext, frame);
}

//
// Process request data frame (Master -> Slave data request)
//
NTSTATUS RS485ProcessRequestDataFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame)
{
    if (deviceContext == NULL || frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    RS485_DEBUG_PRINT("Processing request data frame");

    // Validate frame format
    if (frame->Header != RS485_FRAME_HEADER || frame->Trailer != RS485_FRAME_TRAILER) {
        RS485HandleFrameError(deviceContext, frame, RS485_ERROR_FRAME_ERROR);
        return STATUS_INVALID_PARAMETER;
    }

    // Store request in pending queue for response tracking
    // This is a simplified implementation - full version would manage request queue
    deviceContext->PendingRequests++;

    // Route to appropriate handler
    return RS485RouteFrameToAPICategory(deviceContext, frame);
}

//
// Process response frame (Slave -> Master response)
//
NTSTATUS RS485ProcessResponseFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame)
{
    if (deviceContext == NULL || frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    RS485_DEBUG_PRINT("Processing response frame");

    // Validate frame format
    if (frame->Header != RS485_FRAME_HEADER || frame->Trailer != RS485_FRAME_TRAILER) {
        RS485HandleFrameError(deviceContext, frame, RS485_ERROR_FRAME_ERROR);
        return STATUS_INVALID_PARAMETER;
    }

    // Store response data in downlink buffer using proper buffer functions
    NTSTATUS status = RS485PushPayload(deviceContext->DownlinkBuffer, frame->Payload);
    if (!NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Failed to push payload to downlink buffer: 0x%08X", status);
    }
    if (!NT_SUCCESS(status)) {
        RS485HandleBufferOverflow(deviceContext, BufferTypeDownlink, frame->Payload);
        return status;
    }

    // Update response statistics
    deviceContext->PendingRequests = (deviceContext->PendingRequests > 0) ? 
                                     deviceContext->PendingRequests - 1 : 0;
    // Use GetSystemTimeAsFileTime for UMDF (not KeQuerySystemTime which is kernel-mode)
    FILETIME fileTime;
    GetSystemTimeAsFileTime(&fileTime);
    deviceContext->HardwareStatus.LastResponseTime.QuadPart =
        ((LARGE_INTEGER*)&fileTime)->QuadPart;

    return STATUS_SUCCESS;
}

//
// Process resend request frame (bidirectional error recovery)
//
NTSTATUS RS485ProcessResendRequestFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame)
{
    if (deviceContext == NULL || frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    RS485_DEBUG_PRINT("Processing resend request frame");

    // Validate frame format
    if (frame->Header != RS485_FRAME_HEADER || frame->Trailer != RS485_FRAME_TRAILER) {
        RS485HandleFrameError(deviceContext, frame, RS485_ERROR_FRAME_ERROR);
        return STATUS_INVALID_PARAMETER;
    }

    // Extract sequence number or frame ID from payload for resend identification
    UINT32 resendId = *(UINT32*)frame->Payload;
    
    RS485_DEBUG_PRINT("Resend requested for frame ID: %u", resendId);

    // Look up frame in transmission history and resend if found
    // This is a simplified implementation - full version would maintain transmission history
    deviceContext->ErrorStatistics.RetransmissionRequests++;

    return STATUS_SUCCESS;
}

//
// Helper function to store frame in buffer
//
NTSTATUS RS485StoreFrameInBuffer(PRS485_BUFFER buffer, const PRS485_FRAME frame)
{
    if (buffer == NULL || frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Check if buffer has space (simplified check)
    if (buffer->CurrentFrameCount >= buffer->MaxFrameCount) {
        buffer->OverflowCount++;
        return STATUS_BUFFER_OVERFLOW;
    }

    // Store payload data (12 bytes) in buffer
    UINT32 writeIndex = buffer->WriteIndex;
    RtlCopyMemory(&buffer->Data[writeIndex * RS485_PAYLOAD_SIZE], 
                  frame->Payload, 
                  RS485_PAYLOAD_SIZE);

    // Update buffer indices
    buffer->WriteIndex = (writeIndex + 1) % buffer->MaxFrameCount;
    buffer->CurrentFrameCount++;
    buffer->TotalFramesStored++;

    return STATUS_SUCCESS;
}

//
// Placeholder implementations for specific command handlers
//
NTSTATUS RS485ProcessSystemConfigFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame)
{
    UNREFERENCED_PARAMETER(deviceContext);
    UNREFERENCED_PARAMETER(frame);
    RS485_DEBUG_PRINT("System config frame processing - placeholder implementation");
    return STATUS_SUCCESS;
}

NTSTATUS RS485ProcessUserConfigFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame)
{
    UNREFERENCED_PARAMETER(deviceContext);
    UNREFERENCED_PARAMETER(frame);
    RS485_DEBUG_PRINT("User config frame processing - placeholder implementation");
    return STATUS_SUCCESS;
}

NTSTATUS RS485ProcessDataQueryFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame)
{
    UNREFERENCED_PARAMETER(deviceContext);
    UNREFERENCED_PARAMETER(frame);
    RS485_DEBUG_PRINT("Data query frame processing - placeholder implementation");
    return STATUS_SUCCESS;
}

NTSTATUS RS485ProcessModelDataFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame)
{
    UNREFERENCED_PARAMETER(deviceContext);
    UNREFERENCED_PARAMETER(frame);
    RS485_DEBUG_PRINT("Model data frame processing - placeholder implementation");
    return STATUS_SUCCESS;
}
