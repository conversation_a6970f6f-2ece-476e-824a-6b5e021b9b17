//
// RS485DriverInstaller.cpp - Complete RS485 Driver Installation Program
// Installs FTDI VCP Driver + RS485 UMDF Filter Driver
//

#include <windows.h>
#include <iostream>
#include <string>
#include <filesystem>
#include <setupapi.h>
#include <newdev.h>
#include <cfgmgr32.h>
#include <winnt.h>

#pragma comment(lib, "setupapi.lib")
#pragma comment(lib, "newdev.lib")
#pragma comment(lib, "cfgmgr32.lib")

class RS485DriverInstaller {
private:
    std::wstring m_installPath;
    std::wstring m_tempPath;
    bool m_isElevated;

public:
    RS485DriverInstaller() {
        // Get current executable path
        wchar_t exePath[MAX_PATH];
        GetModuleFileNameW(nullptr, exePath, MAX_PATH);
        std::filesystem::path currentPath(exePath);
        m_installPath = currentPath.parent_path().wstring();
        
        // Create temp directory
        wchar_t tempPath[MAX_PATH];
        GetTempPathW(MAX_PATH, tempPath);
        m_tempPath = std::wstring(tempPath) + L"RS485Driver\\";
        CreateDirectoryW(m_tempPath.c_str(), nullptr);
        
        // Check if running as administrator
        m_isElevated = IsElevated();
    }

    ~RS485DriverInstaller() {
        // Cleanup temp directory
        std::filesystem::remove_all(m_tempPath);
    }

    bool IsElevated() {
        BOOL isElevated = FALSE;
        HANDLE token = nullptr;
        
        if (OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &token)) {
            TOKEN_ELEVATION elevation;
            DWORD size = sizeof(TOKEN_ELEVATION);
            
            if (GetTokenInformation(token, TokenElevation, &elevation, sizeof(elevation), &size)) {
                isElevated = elevation.TokenIsElevated;
            }
            CloseHandle(token);
        }
        
        return isElevated == TRUE;
    }

    bool RestartAsAdmin() {
        wchar_t exePath[MAX_PATH];
        GetModuleFileNameW(nullptr, exePath, MAX_PATH);
        
        SHELLEXECUTEINFOW sei = { sizeof(sei) };
        sei.lpVerb = L"runas";
        sei.lpFile = exePath;
        sei.hwnd = nullptr;
        sei.nShow = SW_NORMAL;
        
        if (!ShellExecuteExW(&sei)) {
            DWORD error = GetLastError();
            if (error == ERROR_CANCELLED) {
                std::wcout << L"Installation cancelled by user." << std::endl;
            }
            return false;
        }
        
        return true;
    }

    bool ExtractEmbeddedFiles() {
        std::wcout << L"Extracting installation files..." << std::endl;

        // Extract FTDI driver (embedded as resource)
        if (!ExtractResource(MAKEINTRESOURCEW(101), L"BINARY", m_tempPath + L"CDM2123620_Setup.exe")) {
            std::wcout << L"Failed to extract FTDI driver!" << std::endl;
            return false;
        }

        // Extract our driver files (embedded as resources)
        if (!ExtractResource(MAKEINTRESOURCEW(102), L"BINARY", m_tempPath + L"RS485FilterDriver.dll")) {
            std::wcout << L"Failed to extract RS485 driver DLL!" << std::endl;
            return false;
        }

        if (!ExtractResource(MAKEINTRESOURCEW(103), L"BINARY", m_tempPath + L"RS485FilterDriver.inf")) {
            std::wcout << L"Failed to extract RS485 driver INF!" << std::endl;
            return false;
        }

        if (!ExtractResource(MAKEINTRESOURCEW(104), L"BINARY", m_tempPath + L"RS485FilterDriver.cat")) {
            std::wcout << L"Failed to extract RS485 driver CAT!" << std::endl;
            return false;
        }

        std::wcout << L"All files extracted successfully." << std::endl;
        return true;
    }

    bool ExtractResource(const wchar_t* name, const wchar_t* type, const std::wstring& outputPath) {
        HRSRC hResource = FindResourceW(nullptr, name, type);
        if (!hResource) return false;
        
        HGLOBAL hLoadedResource = LoadResource(nullptr, hResource);
        if (!hLoadedResource) return false;
        
        LPVOID pLockedResource = LockResource(hLoadedResource);
        if (!pLockedResource) return false;
        
        DWORD resourceSize = SizeofResource(nullptr, hResource);
        if (resourceSize == 0) return false;
        
        HANDLE hFile = CreateFileW(
            outputPath.c_str(),
            GENERIC_WRITE,
            0,
            nullptr,
            CREATE_ALWAYS,
            FILE_ATTRIBUTE_NORMAL,
            nullptr
        );
        
        if (hFile == INVALID_HANDLE_VALUE) return false;
        
        DWORD bytesWritten;
        bool success = WriteFile(hFile, pLockedResource, resourceSize, &bytesWritten, nullptr);
        CloseHandle(hFile);
        
        return success && (bytesWritten == resourceSize);
    }

    bool IsValidExecutable(const std::wstring& filePath) {
        // Check if file exists and is a valid PE executable
        HANDLE hFile = CreateFileW(filePath.c_str(), GENERIC_READ, FILE_SHARE_READ,
                                   nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
        if (hFile == INVALID_HANDLE_VALUE) {
            return false;
        }

        // Read DOS header
        IMAGE_DOS_HEADER dosHeader;
        DWORD bytesRead;
        if (!ReadFile(hFile, &dosHeader, sizeof(dosHeader), &bytesRead, nullptr) ||
            bytesRead != sizeof(dosHeader) || dosHeader.e_magic != IMAGE_DOS_SIGNATURE) {
            CloseHandle(hFile);
            return false;
        }

        CloseHandle(hFile);
        return true;
    }

    bool InstallFTDIDriver() {
        std::wcout << L"Checking FTDI VCP Driver..." << std::endl;

        std::wstring ftdiInstaller = m_tempPath + L"CDM2123620_Setup.exe";

        // Check if the FTDI driver is valid
        if (!IsValidExecutable(ftdiInstaller)) {
            std::wcout << L"WARNING: FTDI driver is not available or invalid." << std::endl;
            std::wcout << L"This is a development build with placeholder FTDI driver." << std::endl;
            std::wcout << L"" << std::endl;
            std::wcout << L"For production use:" << std::endl;
            std::wcout << L"1. Download CDM2123620_Setup.exe from https://ftdichip.com/drivers/vcp-drivers/" << std::endl;
            std::wcout << L"2. Replace the placeholder file in Installer directory" << std::endl;
            std::wcout << L"3. Rebuild the installer" << std::endl;
            std::wcout << L"" << std::endl;
            std::wcout << L"Continuing with RS485 driver installation only..." << std::endl;
            return true; // Continue with RS485 driver installation
        }

        std::wcout << L"Installing FTDI VCP Driver..." << std::endl;

        SHELLEXECUTEINFOW sei = { sizeof(sei) };
        sei.fMask = SEE_MASK_NOCLOSEPROCESS | SEE_MASK_NOASYNC;
        sei.lpFile = ftdiInstaller.c_str();
        sei.lpParameters = L"/S"; // Silent installation
        sei.nShow = SW_HIDE;

        if (!ShellExecuteExW(&sei)) {
            std::wcout << L"Failed to start FTDI installer!" << std::endl;
            return false;
        }

        // Wait for installation to complete
        WaitForSingleObject(sei.hProcess, INFINITE);

        DWORD exitCode;
        GetExitCodeProcess(sei.hProcess, &exitCode);
        CloseHandle(sei.hProcess);

        if (exitCode == 0) {
            std::wcout << L"FTDI driver installed successfully." << std::endl;
            return true;
        } else {
            std::wcout << L"FTDI driver installation failed with code: " << exitCode << std::endl;
            return false;
        }
    }

    bool InstallRS485Driver() {
        std::wcout << L"Installing RS485 Filter Driver..." << std::endl;
        
        std::wstring infPath = m_tempPath + L"RS485FilterDriver.inf";
        
        // Install the driver package
        if (!SetupCopyOEMInfW(
            infPath.c_str(),
            nullptr,
            SPOST_PATH,
            0,
            nullptr,
            0,
            nullptr,
            nullptr
        )) {
            DWORD error = GetLastError();
            std::wcout << L"Failed to install driver package. Error: " << error << std::endl;
            return false;
        }
        
        std::wcout << L"RS485 driver installed successfully." << std::endl;
        return true;
    }

    bool Install() {
        std::wcout << L"=== RS485 Driver Installation ===" << std::endl;
        std::wcout << L"This will install:" << std::endl;
        std::wcout << L"1. FTDI VCP Driver (CDM2123620)" << std::endl;
        std::wcout << L"2. RS485 Protocol Filter Driver" << std::endl;
        std::wcout << std::endl;
        
        if (!m_isElevated) {
            std::wcout << L"Administrator privileges required. Restarting..." << std::endl;
            return RestartAsAdmin();
        }
        
        // Step 1: Extract files
        if (!ExtractEmbeddedFiles()) {
            std::wcout << L"Failed to extract installation files!" << std::endl;
            return false;
        }
        
        // Step 2: Install FTDI driver
        if (!InstallFTDIDriver()) {
            std::wcout << L"FTDI driver installation failed!" << std::endl;
            return false;
        }
        
        // Step 3: Install our RS485 driver
        if (!InstallRS485Driver()) {
            std::wcout << L"RS485 driver installation failed!" << std::endl;
            return false;
        }
        
        std::wcout << L"=== Installation Complete ===" << std::endl;
        std::wcout << L"RS485 Driver has been successfully installed!" << std::endl;
        std::wcout << L"Please connect your FTDI device to test the installation." << std::endl;
        
        return true;
    }
};

int wmain(int argc, wchar_t* argv[]) {
    RS485DriverInstaller installer;
    
    if (!installer.Install()) {
        std::wcout << L"Installation failed!" << std::endl;
        std::wcout << L"Press any key to exit..." << std::endl;
        _getwch();
        return 1;
    }
    
    std::wcout << L"Press any key to exit..." << std::endl;
    _getwch();
    return 0;
}
