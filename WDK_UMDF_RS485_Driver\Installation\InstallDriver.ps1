#
# AI-SLDAP RS485 Driver Installation Script (PowerShell)
# Integrates FTDI VCP Driver with UMDF RS485 Filter Driver
#
# This script must be run as Administrator

param(
    [switch]$Silent = $false,
    [switch]$SkipFTDI = $false
)

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as administrator'" -ForegroundColor Yellow
    if (-not $Silent) { Read-Host "Press Enter to exit" }
    exit 1
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "AI-SLDAP RS485 Driver Installation" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# System information
$osInfo = Get-WmiObject -Class Win32_OperatingSystem
$arch = if ([Environment]::Is64BitOperatingSystem) { "x64" } else { "x86" }

Write-Host "System Information:" -ForegroundColor Green
Write-Host "  OS: $($osInfo.Caption)" -ForegroundColor White
Write-Host "  Version: $($osInfo.Version)" -ForegroundColor White
Write-Host "  Architecture: $arch" -ForegroundColor White
Write-Host ""

# Function to check if a file exists
function Test-FileExists {
    param([string]$FilePath, [string]$Description)
    
    if (-not (Test-Path $FilePath)) {
        Write-Host "ERROR: $Description not found!" -ForegroundColor Red
        Write-Host "Expected location: $FilePath" -ForegroundColor Yellow
        return $false
    }
    return $true
}

# Function to install FTDI driver
function Install-FTDIDriver {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Step 1: Installing FTDI VCP Driver" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    if (-not (Test-FileExists "CDM2123620_Setup.exe" "FTDI driver installer")) {
        return $false
    }
    
    Write-Host "Installing FTDI VCP Driver..." -ForegroundColor Yellow
    Write-Host "This may take a few minutes and may require a reboot." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        $process = Start-Process -FilePath "CDM2123620_Setup.exe" -ArgumentList "/S" -Wait -PassThru
        
        if ($process.ExitCode -eq 0) {
            Write-Host "FTDI VCP Driver installation completed successfully." -ForegroundColor Green
        } else {
            Write-Host "WARNING: FTDI driver installation returned exit code $($process.ExitCode)" -ForegroundColor Yellow
            Write-Host "Please check if the driver was installed correctly." -ForegroundColor Yellow
        }
    } catch {
        Write-Host "ERROR: Failed to install FTDI driver: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    return $true
}

# Function to install RS485 filter driver
function Install-RS485Driver {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Step 2: Installing RS485 Filter Driver" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    # Check required files
    $requiredFiles = @(
        @{Path="RS485FilterDriver.dll"; Description="RS485 Filter Driver DLL"},
        @{Path="RS485FilterDriver.inf"; Description="RS485 Filter Driver INF"}
    )
    
    foreach ($file in $requiredFiles) {
        if (-not (Test-FileExists $file.Path $file.Description)) {
            return $false
        }
    }
    
    Write-Host "Installing AI-SLDAP RS485 Filter Driver..." -ForegroundColor Yellow
    
    try {
        # Install driver using pnputil
        $result = & pnputil /add-driver "RS485FilterDriver.inf" /install 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "RS485 Filter Driver installation completed successfully." -ForegroundColor Green
        } else {
            Write-Host "ERROR: Failed to install RS485 Filter Driver!" -ForegroundColor Red
            Write-Host "pnputil output: $result" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "ERROR: Failed to install RS485 driver: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    return $true
}

# Function to verify installation
function Test-Installation {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Step 3: Verifying Installation" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    Write-Host "Checking installed drivers..." -ForegroundColor Yellow
    
    try {
        # Check for our driver in the driver store
        $drivers = & pnputil /enum-drivers | Select-String -Pattern "RS485"
        
        if ($drivers) {
            Write-Host "RS485 driver found in driver store:" -ForegroundColor Green
            $drivers | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
        } else {
            Write-Host "WARNING: RS485 driver not found in driver store" -ForegroundColor Yellow
        }
        
        Write-Host ""
        
        # Check for FTDI devices
        Write-Host "Checking for FTDI devices..." -ForegroundColor Yellow
        
        $ftdiDevices = Get-WmiObject -Class Win32_PnPEntity | Where-Object { $_.DeviceID -like "*VID_0403*" }
        
        if ($ftdiDevices) {
            Write-Host "FTDI devices found:" -ForegroundColor Green
            $ftdiDevices | ForEach-Object { 
                Write-Host "  $($_.Name) - $($_.DeviceID)" -ForegroundColor White 
            }
        } else {
            Write-Host "No FTDI devices currently connected." -ForegroundColor Yellow
            Write-Host "Please connect your RS485 device to complete setup." -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "WARNING: Could not verify installation: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

# Main installation process
try {
    # Install FTDI driver (unless skipped)
    if (-not $SkipFTDI) {
        if (-not (Install-FTDIDriver)) {
            Write-Host "FTDI driver installation failed. Continuing with RS485 driver..." -ForegroundColor Yellow
        }
    } else {
        Write-Host "Skipping FTDI driver installation as requested." -ForegroundColor Yellow
        Write-Host ""
    }
    
    # Install RS485 driver
    if (-not (Install-RS485Driver)) {
        Write-Host "RS485 driver installation failed!" -ForegroundColor Red
        if (-not $Silent) { Read-Host "Press Enter to exit" }
        exit 1
    }
    
    # Verify installation
    Test-Installation
    
    # Success message
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Installation Complete!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "The AI-SLDAP RS485 Driver has been successfully installed." -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Connect your RS485 device via USB" -ForegroundColor White
    Write-Host "2. Windows will automatically detect and configure the device" -ForegroundColor White
    Write-Host "3. Use the provided test application to verify functionality" -ForegroundColor White
    Write-Host ""
    Write-Host "If you encounter any issues:" -ForegroundColor Cyan
    Write-Host "- Check Device Manager for any warning signs" -ForegroundColor White
    Write-Host "- Ensure your RS485 device is properly connected" -ForegroundColor White
    Write-Host "- Run the test application as Administrator" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "FATAL ERROR: $($_.Exception.Message)" -ForegroundColor Red
    if (-not $Silent) { Read-Host "Press Enter to exit" }
    exit 1
}

if (-not $Silent) {
    Read-Host "Press Enter to exit"
}

Write-Host "Installation script completed." -ForegroundColor Green
