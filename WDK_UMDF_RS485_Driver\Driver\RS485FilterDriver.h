#ifndef RS485_FILTER_DRIVER_H
#define RS485_FILTER_DRIVER_H

//
// RS485 Filter Driver Header
// Windows User-Mode Driver Framework (UMDF) Implementation
//

#include "../Include/RS485Common.h"
#include "../Include/RS485Protocol.h"
#include "../Include/RS485Errors.h"

//
// WDF and UMDF Headers for UMDF 2.0
//
#include <wdf.h>
#include <wdfdriver.h>
#include <wdfdevice.h>
#include <wdfio.h>

//
// Driver Context Structures
//

//
// Hardware Status Structure
//
typedef struct _RS485_HARDWARE_STATUS {
    BOOLEAN IsConnected;            // Device connection status
    BOOLEAN IsActive;               // Device active status
    LARGE_INTEGER LastResponseTime; // Last response timestamp
    LARGE_INTEGER LastTimeout;      // Last timeout timestamp
    LARGE_INTEGER LastCrcError;     // Last CRC error timestamp
    BOOLEAN FrameSyncLost;          // Frame synchronization status
    UINT32 TotalFramesSent;         // Total frames transmitted
    UINT32 TotalFramesReceived;     // Total frames received
    UINT32 CrcErrors;               // CRC error count
    UINT32 TimeoutErrors;           // Timeout error count
    UINT32 BufferOverflows;         // Buffer overflow count
} RS485_HARDWARE_STATUS, *PRS485_HARDWARE_STATUS;

//
// Buffer Status Structure for IOCTL
//
typedef struct _RS485_BUFFER_STATUS {
    UINT32 UplinkCurrentFrames;     // Current frames in uplink buffer
    UINT32 UplinkMaxFrames;         // Maximum uplink buffer capacity
    UINT32 UplinkOverflowCount;     // Number of uplink overflows
    UINT32 UplinkTotalFrames;       // Total frames stored in uplink

    UINT32 DownlinkCurrentFrames;   // Current frames in downlink buffer
    UINT32 DownlinkMaxFrames;       // Maximum downlink buffer capacity
    UINT32 DownlinkOverflowCount;   // Number of downlink overflows
    UINT32 DownlinkTotalFrames;     // Total frames stored in downlink
} RS485_BUFFER_STATUS, *PRS485_BUFFER_STATUS;

//
// Simple Buffer Structure for frame storage
//
typedef struct _RS485_BUFFER {
    UINT8   Data[10 * RS485_PAYLOAD_SIZE];  // Buffer data (max 10 frames * 12 bytes)
    UINT32  ReadIndex;                      // Read position
    UINT32  WriteIndex;                     // Write position
    UINT32  CurrentFrameCount;              // Current number of frames
    UINT32  MaxFrameCount;                  // Maximum frame capacity
    UINT32  OverflowCount;                  // Overflow counter
    UINT32  TotalFramesStored;              // Total frames stored
} RS485_BUFFER, *PRS485_BUFFER;

// Device Context - Per device instance
typedef struct _RS485_DEVICE_CONTEXT {
    // Device identification
    WDFDEVICE               Device;
    WDFIOTARGET             LowerDeviceTarget;      // FTDI VCP driver target
    
    // Buffer management (payload-focused)
    PRS485_BUFFER_CONTEXT   UplinkBuffer;          // 5 × 12 bytes = 60 bytes
    PRS485_BUFFER_CONTEXT   DownlinkBuffer;        // 10 × 12 bytes = 120 bytes
    
    // Buffer flags for overflow prevention
    volatile BOOLEAN        UplinkBufferFull;      // Uplink buffer full flag
    volatile BOOLEAN        DownlinkBufferFull;    // Downlink buffer full flag
    
    // Frame processing
    RS485_FRAME_STATE       FrameState;            // Current frame processing state
    RS485_FRAME             CurrentFrame;          // Frame being processed
    SIZE_T                  BytesReceived;         // Bytes received for current frame
    
    // Synchronization
    WDFWAITLOCK             BufferLock;            // Protects buffer access
    WDFWAITLOCK             FrameLock;             // Protects frame processing
    
    // Work items for asynchronous processing
    WDFWORKITEM             ReceiveWorkItem;       // Asynchronous receive processing
    WDFWORKITEM             TransmitWorkItem;      // Asynchronous transmit processing
    
    // Statistics and performance
    RS485_HARDWARE_STATUS   HardwareStatus;       // Hardware status information
    RS485_PERFORMANCE_METRICS PerformanceMetrics; // Performance metrics
    RS485_ERROR_STATISTICS  ErrorStatistics;      // Error statistics
    
    // Configuration
    RS485_BUFFER_OVERFLOW_POLICY OverflowPolicy;  // Buffer overflow policy
    UINT32                  BufferThresholdPercent; // Buffer threshold percentage
    UINT32                  ResponseTimeoutMs;     // Response timeout in milliseconds
    UINT8                   CurrentSlaveAddress;   // Current slave device address
    UINT32                  PendingRequests;       // Number of pending requests
    
    // Device interface
    WDFSTRING               DeviceInterface;       // Device interface string
    
} RS485_DEVICE_CONTEXT, *PRS485_DEVICE_CONTEXT;

WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(RS485_DEVICE_CONTEXT, RS485GetDeviceContext)

// I/O Queue Context - Per queue instance
typedef struct _RS485_QUEUE_CONTEXT {
    WDFQUEUE                Queue;
    PRS485_DEVICE_CONTEXT   DeviceContext;
    WDFCOLLECTION           PendingRequests;       // Collection of pending requests
    WDFWAITLOCK             QueueLock;             // Protects queue operations
} RS485_QUEUE_CONTEXT, *PRS485_QUEUE_CONTEXT;

WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(RS485_QUEUE_CONTEXT, RS485GetQueueContext)

// Buffer Context - Payload buffer management
typedef struct _RS485_BUFFER_CONTEXT {
    UINT32                  Signature;             // Buffer signature for validation
    UINT8*                  Buffer;                // Buffer memory
    SIZE_T                  Capacity;              // Number of payload slots
    SIZE_T                  PayloadSize;           // Size per payload (12 bytes)
    SIZE_T                  Head;                  // Write position
    SIZE_T                  Tail;                  // Read position
    SIZE_T                  Count;                 // Current payload count
    WDFWAITLOCK             Lock;                  // Buffer synchronization
    UINT32                  SequenceNumber;        // For FIFO integrity verification
} RS485_BUFFER_CONTEXT, *PRS485_BUFFER_CONTEXT;

// Request Context - Per I/O request
typedef struct _RS485_REQUEST_CONTEXT {
    WDFREQUEST              Request;
    PRS485_DEVICE_CONTEXT   DeviceContext;
    UINT32                  IOCTLCode;
    PVOID                   InputBuffer;
    SIZE_T                  InputBufferLength;
    PVOID                   OutputBuffer;
    SIZE_T                  OutputBufferLength;
    LARGE_INTEGER           StartTime;             // For timeout tracking
} RS485_REQUEST_CONTEXT, *PRS485_REQUEST_CONTEXT;

WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(RS485_REQUEST_CONTEXT, RS485GetRequestContext)

//
// Driver Entry Points
//
DRIVER_INITIALIZE DriverEntry;
EVT_WDF_DRIVER_DEVICE_ADD RS485EvtDeviceAdd;
EVT_WDF_OBJECT_CONTEXT_CLEANUP RS485EvtDriverContextCleanup;

//
// Device Event Callbacks
//
EVT_WDF_DEVICE_PREPARE_HARDWARE RS485EvtDevicePrepareHardware;
EVT_WDF_DEVICE_RELEASE_HARDWARE RS485EvtDeviceReleaseHardware;
EVT_WDF_DEVICE_D0_ENTRY RS485EvtDeviceD0Entry;
EVT_WDF_DEVICE_D0_EXIT RS485EvtDeviceD0Exit;

//
// I/O Queue Event Callbacks
//
EVT_WDF_IO_QUEUE_IO_DEVICE_CONTROL RS485EvtIoDeviceControl;
EVT_WDF_IO_QUEUE_IO_STOP RS485EvtIoStop;

//
// Work Item Callbacks
//
EVT_WDF_WORKITEM RS485EvtReceiveWorkItem;
EVT_WDF_WORKITEM RS485EvtTransmitWorkItem;

//
// Buffer Management Functions
//
NTSTATUS RS485CreateBuffer(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                          _In_ SIZE_T PayloadCount,
                          _Out_ PRS485_BUFFER_CONTEXT* BufferContext);

VOID RS485DestroyBuffer(_In_ PRS485_BUFFER_CONTEXT BufferContext);

NTSTATUS RS485PushPayload(_In_ PRS485_BUFFER_CONTEXT BufferContext,
                         _In_reads_bytes_(RS485_PAYLOAD_SIZE) const UINT8* PayloadData);

NTSTATUS RS485PopPayload(_In_ PRS485_BUFFER_CONTEXT BufferContext,
                        _Out_writes_bytes_(RS485_PAYLOAD_SIZE) UINT8* PayloadData);

BOOLEAN RS485IsBufferFull(_In_ PRS485_BUFFER_CONTEXT BufferContext);
BOOLEAN RS485IsBufferEmpty(_In_ PRS485_BUFFER_CONTEXT BufferContext);
VOID RS485ClearBuffer(_In_ PRS485_BUFFER_CONTEXT BufferContext);

SIZE_T RS485GetBufferUsedCount(_In_ PRS485_BUFFER_CONTEXT BufferContext);
SIZE_T RS485GetBufferTotalCount(_In_ PRS485_BUFFER_CONTEXT BufferContext);

//
// Frame Processing Functions
//
NTSTATUS RS485InitializeFrameProcessor(_In_ PRS485_DEVICE_CONTEXT DeviceContext);
VOID RS485CleanupFrameProcessor(_In_ PRS485_DEVICE_CONTEXT DeviceContext);

RS485_FRAME_PROCESS_RESULT RS485ProcessIncomingByte(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                                   _In_ UINT8 Byte);

NTSTATUS RS485ProcessCompleteFrame(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                  _In_ const RS485_FRAME* Frame);

NTSTATUS RS485BuildFrame(_In_ const RS485_PAYLOAD* Payload,
                        _In_ UINT8 FunctionCode,
                        _In_ UINT8 DeviceAddress,
                        _Out_ PRS485_FRAME Frame);

//
// Protocol Processing Functions
//
NTSTATUS RS485RouteFrameToAPICategory(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                     _In_ const RS485_FRAME* Frame);

NTSTATUS RS485ProcessAssignDataFrame(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                    _In_ const RS485_FRAME* Frame);

NTSTATUS RS485ProcessRequestDataFrame(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                     _In_ const RS485_FRAME* Frame);

NTSTATUS RS485ProcessResponseFrame(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                  _In_ const RS485_FRAME* Frame);

NTSTATUS RS485ProcessResendRequestFrame(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                       _In_ const RS485_FRAME* Frame);

//
// IOCTL Handler Functions
//
NTSTATUS RS485HandleSystemConfigIOCTL(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                     _In_ WDFREQUEST Request,
                                     _In_ size_t InputBufferLength,
                                     _In_ size_t OutputBufferLength);

NTSTATUS RS485HandleUserConfigIOCTL(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                   _In_ WDFREQUEST Request,
                                   _In_ size_t InputBufferLength,
                                   _In_ size_t OutputBufferLength);

NTSTATUS RS485HandleDataRequestIOCTL(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                    _In_ WDFREQUEST Request,
                                    _In_ size_t InputBufferLength,
                                    _In_ size_t OutputBufferLength);

NTSTATUS RS485HandleReceiveResponseIOCTL(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                        _In_ WDFREQUEST Request,
                                        _In_ size_t InputBufferLength,
                                        _In_ size_t OutputBufferLength);

NTSTATUS RS485HandleBufferStatusIOCTL(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                     _In_ WDFREQUEST Request,
                                     _In_ size_t InputBufferLength,
                                     _In_ size_t OutputBufferLength);

//
// Utility Functions
//
NTSTATUS RS485SendFrameToLowerDriver(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                    _In_ const RS485_FRAME* Frame);

NTSTATUS RS485CheckBufferFlags(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                              _In_ RS485_BUFFER_TYPE BufferType,
                              _Out_ PBOOLEAN IsBufferFull);

VOID RS485UpdateBufferFlags(_In_ PRS485_DEVICE_CONTEXT DeviceContext);

VOID RS485UpdatePerformanceMetrics(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                  _In_ BOOLEAN IsSuccess,
                                  _In_ UINT32 ResponseTimeMs);

VOID RS485LogFrameError(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                       _In_ const RS485_FRAME* Frame,
                       _In_ PCSTR Reason);

//
// CRC and Validation Functions
//
UINT8 RS485CalculateFrameCRC(_In_ const RS485_FRAME* Frame);
BOOLEAN RS485ValidateFrame(_In_ const RS485_FRAME* Frame);
BOOLEAN RS485ValidatePayload(_In_ const RS485_PAYLOAD* Payload);

//
// Error Handling Functions
//
const char* RS485GetErrorString(RS485_ERROR error);
void RS485InitializeErrorStatistics(PRS485_ERROR_STATISTICS statistics);
void RS485UpdateErrorStatistics(PRS485_ERROR_STATISTICS statistics, RS485_ERROR error);
VOID RS485HandleFrameError(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                          _In_ const RS485_FRAME* Frame,
                          _In_ RS485_ERROR Error);
void RS485HandleBufferOverflow(PRS485_DEVICE_CONTEXT deviceContext, RS485_BUFFER_TYPE bufferType, const UINT8* data);

VOID RS485HandleBufferOverflow(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                              _In_ RS485_BUFFER_TYPE BufferType,
                              _In_ const UINT8* PayloadData);

NTSTATUS RS485ScheduleResendRequest(_In_ PRS485_DEVICE_CONTEXT DeviceContext,
                                   _In_ const RS485_FRAME* OriginalFrame);

//
// Protocol Processing Functions
//
NTSTATUS RS485RouteFrameToAPICategory(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame);
NTSTATUS RS485ProcessAssignDataFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame);
NTSTATUS RS485ProcessRequestDataFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame);
NTSTATUS RS485ProcessResponseFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame);
NTSTATUS RS485ProcessResendRequestFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame);
NTSTATUS RS485StoreFrameInBuffer(PRS485_BUFFER buffer, const PRS485_FRAME frame);
NTSTATUS RS485ProcessSystemConfigFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame);
NTSTATUS RS485ProcessUserConfigFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame);
NTSTATUS RS485ProcessDataQueryFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame);
NTSTATUS RS485ProcessModelDataFrame(PRS485_DEVICE_CONTEXT deviceContext, const PRS485_FRAME frame);

//
// Debug and Diagnostics
//
#if DBG
VOID RS485DumpFrame(_In_ const RS485_FRAME* Frame, _In_ PCSTR Context);
VOID RS485DumpPayload(_In_ const RS485_PAYLOAD* Payload, _In_ PCSTR Context);
VOID RS485DumpBufferStatus(_In_ PRS485_DEVICE_CONTEXT DeviceContext);
#else
#define RS485DumpFrame(Frame, Context)
#define RS485DumpPayload(Payload, Context)
#define RS485DumpBufferStatus(DeviceContext)
#endif

#endif // RS485_FILTER_DRIVER_H
