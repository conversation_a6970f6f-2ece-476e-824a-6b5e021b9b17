#ifndef RS485_INTERFACE_COMMON_H
#define RS485_INTERFACE_COMMON_H

//
// RS485 Driver Interface Common Definitions
// User-mode interface definitions without WDF dependencies
//

#include <windows.h>
#include <strsafe.h>

//
// Protocol Constants
//
#define RS485_FRAME_HEADER          0xAA
#define RS485_FRAME_TRAILER         0x0D
#define RS485_PAYLOAD_SIZE          12      // 12-byte payload (4-byte key + 8-byte data)
#define RS485_FRAME_SIZE            16      // Total frame size including header, ID, payload, CRC, trailer
#define RS485_CRC_POLYNOMIAL        0x07    // CRC-8 polynomial

//
// Buffer Configuration
//
#define RS485_UPLINK_BUFFER_SLOTS   5       // PC to device buffer capacity (5 × 12 bytes = 60 bytes)
#define RS485_DOWNLINK_BUFFER_SLOTS 10      // Device to PC buffer capacity (10 × 12 bytes = 120 bytes)
#define RS485_TOTAL_BUFFER_SIZE     (RS485_UPLINK_BUFFER_SLOTS * RS485_PAYLOAD_SIZE + \
                                     RS485_DOWNLINK_BUFFER_SLOTS * RS485_PAYLOAD_SIZE)

//
// Custom Status Codes for Interface
//
#ifndef STATUS_BUFFER_UNDERFLOW
#define STATUS_BUFFER_UNDERFLOW     ((NTSTATUS)0xC0000093L)
#endif
#ifndef STATUS_BUFFER_OVERFLOW
#define STATUS_BUFFER_OVERFLOW      ((NTSTATUS)0x80000005L)
#endif

//
// Device Interface GUID
// {12345678-1234-1234-1234-123456789ABC}
//
DEFINE_GUID(GUID_DEVINTERFACE_RS485_FILTER,
    0x12345678, 0x1234, 0x1234, 0x12, 0x34, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC);

//
// Pool Tags for Memory Allocation
//
#define RS485_POOL_TAG_DEVICE       'D584'  // '485D'
#define RS485_POOL_TAG_BUFFER       'B584'  // '485B'
#define RS485_POOL_TAG_FRAME        'F584'  // '485F'
#define RS485_POOL_TAG_REQUEST      'R584'  // '485R'

//
// Timeout Values (in milliseconds)
//
#define RS485_DEFAULT_TIMEOUT_MS    1000    // Default operation timeout
#define RS485_FRAME_TIMEOUT_MS      100     // Frame processing timeout
#define RS485_RESPONSE_TIMEOUT_MS   500     // Response waiting timeout

//
// Maximum Values
//
#define RS485_MAX_DEVICE_NAME       256     // Maximum device name length
#define RS485_MAX_RETRY_COUNT       3       // Maximum retry attempts
#define RS485_MAX_PENDING_REQUESTS  16      // Maximum pending requests

//
// Buffer Types and Policies
//
typedef enum _RS485_BUFFER_TYPE {
    BufferTypeUplink = 0,       // PC to device buffer
    BufferTypeDownlink,         // Device to PC buffer
    BufferTypeAll,              // Both buffers
    BufferTypeBoth = BufferTypeAll  // Alias for compatibility
} RS485_BUFFER_TYPE;

typedef enum _RS485_BUFFER_OVERFLOW_POLICY {
    OverflowPolicyBlock = 0,    // Block new data when buffer is full
    OverflowPolicyOverwrite,    // Overwrite oldest data when buffer is full
    OverflowPolicyDiscard       // Discard new data when buffer is full
} RS485_BUFFER_OVERFLOW_POLICY;

//
// Device Information Structure
//
typedef struct _RS485_DEVICE_INFO {
    CHAR DevicePath[MAX_PATH];  // Device path
    CHAR FriendlyName[64];      // Friendly name
    UINT32 DeviceId;            // Device ID
    BOOLEAN IsActive;           // Whether device is active
} RS485_DEVICE_INFO, *PRS485_DEVICE_INFO;

//
// Performance Metrics Structure
//
typedef struct _RS485_PERFORMANCE_METRICS {
    UINT32 TotalFramesSent;     // Total frames sent
    UINT32 TotalFramesReceived; // Total frames received
    UINT32 ErrorFrames;         // Error frames
    UINT32 RetransmittedFrames; // Retransmitted frames
    UINT32 OverflowEvents;      // Buffer overflow events
    UINT32 UnderflowEvents;     // Buffer underflow events
    FLOAT AverageResponseTime;  // Average response time (ms)
    FLOAT MaxResponseTime;      // Maximum response time (ms)
    FLOAT MinResponseTime;      // Minimum response time (ms)
    UINT32 TotalBytes;          // Total bytes transferred
    UINT32 TransferRate;        // Transfer rate (bytes/sec)
} RS485_PERFORMANCE_METRICS, *PRS485_PERFORMANCE_METRICS;

//
// Line Status Structure
//
typedef struct _RS485_LINE_STATUS {
    BOOLEAN LineActive;         // Line is active
    BOOLEAN DataReady;          // Data is ready to be read
    BOOLEAN OverrunError;       // Overrun error
    BOOLEAN ParityError;        // Parity error
    BOOLEAN FramingError;       // Framing error
    BOOLEAN BreakDetected;      // Break detected
    UINT32 SignalQuality;       // Signal quality (0-100%)
    UINT32 NoiseLevel;          // Noise level
} RS485_LINE_STATUS, *PRS485_LINE_STATUS;

//
// Request Options Structure
//
typedef struct _RS485_REQUEST_OPTIONS {
    UINT32 TimeoutMs;           // Request timeout in milliseconds
    BOOLEAN RetryOnFailure;     // Whether to retry on failure
    UINT32 MaxRetries;          // Maximum number of retries
    UINT32 RetryDelayMs;        // Delay between retries in milliseconds
} RS485_REQUEST_OPTIONS, *PRS485_REQUEST_OPTIONS;

//
// Forward declaration for RS485_ERROR
//
typedef enum _RS485_ERROR RS485_ERROR;

//
// Callback Function Types
//
typedef void (*ErrorCallbackFn)(RS485_ERROR error, const char* description);
typedef void (*ResponseCallbackFn)(const uint8_t* responseData, uint32_t dataLength);
typedef void (*BufferThresholdCallbackFn)(RS485_BUFFER_TYPE bufferType, uint32_t currentLevel, uint32_t threshold);

//
// Debug and Logging Macros for Interface
//
#if DBG
#define RS485_INTERFACE_DEBUG_PRINT(format, ...) \
    OutputDebugStringA("[RS485Interface] " format "\n")
#else
#define RS485_INTERFACE_DEBUG_PRINT(format, ...)
#endif

#define RS485_INTERFACE_ERROR_PRINT(format, ...) \
    OutputDebugStringA("[RS485Interface ERROR] " format "\n")

#define RS485_INTERFACE_WARNING_PRINT(format, ...) \
    OutputDebugStringA("[RS485Interface WARNING] " format "\n")

//
// DeviceIoControl Codes
//
#define IOCTL_RS485_SEND_FRAME      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_RECEIVE_FRAME   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_STATUS      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_RESET_BUFFERS   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_SET_CONFIG      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_WRITE_ACCESS)
#define IOCTL_RS485_GET_CONFIG      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x805, METHOD_BUFFERED, FILE_READ_ACCESS)

#endif // RS485_INTERFACE_COMMON_H
