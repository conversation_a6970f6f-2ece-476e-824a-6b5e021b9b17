//
// RS485IOCTLHandlers.cpp - IOCTL request handlers
// RS485 UMDF 2.0 Driver for AI-SLDAP Communication
//

#include "RS485FilterDriver.h"

//
// Handle data request IOCTL
//
NTSTATUS RS485HandleDataRequestIOCTL(
    PRS485_DEVICE_CONTEXT deviceContext,
    WDFREQUEST request,
    size_t inputBufferLength,
    size_t outputBufferLength
)
{
    if (deviceContext == NULL || request == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = STATUS_SUCCESS;
    PVOID inputBuffer = NULL;
    PVOID outputBuffer = NULL;

    RS485_DEBUG_PRINT("Handling data request IOCTL");

    // Get input buffer (contains request data)
    if (inputBufferLength > 0) {
        status = WdfRequestRetrieveInputBuffer(request, inputBufferLength, &inputBuffer, NULL);
        if (!NT_SUCCESS(status)) {
            RS485_DEBUG_PRINT("Failed to retrieve input buffer: 0x%08X", status);
            return status;
        }
    }

    // Get output buffer (for response data)
    if (outputBufferLength > 0) {
        status = WdfRequestRetrieveOutputBuffer(request, outputBufferLength, &outputBuffer, NULL);
        if (!NT_SUCCESS(status)) {
            RS485_DEBUG_PRINT("Failed to retrieve output buffer: 0x%08X", status);
            return status;
        }
    }

    // Process the data request
    if (inputBuffer != NULL && inputBufferLength >= RS485_PAYLOAD_SIZE) {
        // Create frame from input data
        RS485_FRAME frame = {0};
        frame.Header = RS485_FRAME_HEADER;
        frame.SlaveId = deviceContext->CurrentSlaveAddress;
        RtlCopyMemory(frame.Payload, inputBuffer, RS485_PAYLOAD_SIZE);
        frame.Trailer = RS485_FRAME_TRAILER;

        // Calculate CRC (simplified implementation)
        frame.Crc = RS485CalculateFrameCRC(&frame);

        // Store frame in uplink buffer for transmission using proper buffer functions
        status = RS485PushPayload(deviceContext->UplinkBuffer, frame.Payload);
        if (!NT_SUCCESS(status)) {
            RS485_DEBUG_PRINT("Failed to push payload to uplink buffer: 0x%08X", status);
        }
        if (!NT_SUCCESS(status)) {
            RS485HandleBufferOverflow(deviceContext, BufferTypeUplink, frame.Payload);
            return status;
        }

        // Update statistics
        deviceContext->HardwareStatus.TotalFramesSent++;
    }

    // Complete the request
    WdfRequestCompleteWithInformation(request, status, 0);
    return status;
}

//
// Handle receive response IOCTL
//
NTSTATUS RS485HandleReceiveResponseIOCTL(
    PRS485_DEVICE_CONTEXT deviceContext,
    WDFREQUEST request,
    size_t inputBufferLength,
    size_t outputBufferLength
)
{
    if (deviceContext == NULL || request == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = STATUS_SUCCESS;
    PVOID outputBuffer = NULL;
    size_t bytesReturned = 0;

    RS485_DEBUG_PRINT("Handling receive response IOCTL");

    // Get output buffer for response data
    if (outputBufferLength < RS485_PAYLOAD_SIZE) {
        WdfRequestComplete(request, STATUS_BUFFER_TOO_SMALL);
        return STATUS_BUFFER_TOO_SMALL;
    }

    status = WdfRequestRetrieveOutputBuffer(request, outputBufferLength, &outputBuffer, NULL);
    if (!NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Failed to retrieve output buffer: 0x%08X", status);
        WdfRequestComplete(request, status);
        return status;
    }

    // Check if response data is available in downlink buffer
    if (!RS485IsBufferEmpty(deviceContext->DownlinkBuffer)) {
        // Retrieve data from buffer using proper buffer functions
        status = RS485PopPayload(deviceContext->DownlinkBuffer, (UINT8*)outputBuffer);
        if (NT_SUCCESS(status)) {
            bytesReturned = RS485_PAYLOAD_SIZE;
            deviceContext->HardwareStatus.TotalFramesReceived++;
            RS485_DEBUG_PRINT("Response data retrieved from buffer");
        }
    } else {
        // No data available
        bytesReturned = 0;
        RS485_DEBUG_PRINT("No response data available");
    }

    // Complete the request
    WdfRequestCompleteWithInformation(request, status, bytesReturned);
    return status;
}

//
// Handle buffer status IOCTL
//
NTSTATUS RS485HandleBufferStatusIOCTL(
    PRS485_DEVICE_CONTEXT deviceContext,
    WDFREQUEST request,
    size_t inputBufferLength,
    size_t outputBufferLength
)
{
    if (deviceContext == NULL || request == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    NTSTATUS status = STATUS_SUCCESS;
    PVOID outputBuffer = NULL;
    size_t bytesReturned = 0;

    UNREFERENCED_PARAMETER(inputBufferLength);

    RS485_DEBUG_PRINT("Handling buffer status IOCTL");

    // Check output buffer size
    if (outputBufferLength < sizeof(RS485_BUFFER_STATUS)) {
        WdfRequestComplete(request, STATUS_BUFFER_TOO_SMALL);
        return STATUS_BUFFER_TOO_SMALL;
    }

    status = WdfRequestRetrieveOutputBuffer(request, outputBufferLength, &outputBuffer, NULL);
    if (!NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Failed to retrieve output buffer: 0x%08X", status);
        WdfRequestComplete(request, status);
        return status;
    }

    // Fill buffer status information
    PRS485_BUFFER_STATUS bufferStatus = (PRS485_BUFFER_STATUS)outputBuffer;
    
    // Uplink buffer status
    bufferStatus->UplinkCurrentFrames = deviceContext->UplinkBuffer.CurrentFrameCount;
    bufferStatus->UplinkMaxFrames = deviceContext->UplinkBuffer.MaxFrameCount;
    bufferStatus->UplinkOverflowCount = deviceContext->UplinkBuffer.OverflowCount;
    bufferStatus->UplinkTotalFrames = deviceContext->UplinkBuffer.TotalFramesStored;

    // Downlink buffer status
    bufferStatus->DownlinkCurrentFrames = deviceContext->DownlinkBuffer.CurrentFrameCount;
    bufferStatus->DownlinkMaxFrames = deviceContext->DownlinkBuffer.MaxFrameCount;
    bufferStatus->DownlinkOverflowCount = deviceContext->DownlinkBuffer.OverflowCount;
    bufferStatus->DownlinkTotalFrames = deviceContext->DownlinkBuffer.TotalFramesStored;

    bytesReturned = sizeof(RS485_BUFFER_STATUS);

    RS485_DEBUG_PRINT("Buffer status: Uplink %u/%u, Downlink %u/%u",
                      bufferStatus->UplinkCurrentFrames, bufferStatus->UplinkMaxFrames,
                      bufferStatus->DownlinkCurrentFrames, bufferStatus->DownlinkMaxFrames);

    // Complete the request
    WdfRequestCompleteWithInformation(request, status, bytesReturned);
    return status;
}

//
// Helper function to calculate frame CRC (simplified implementation)
//
UINT16 RS485CalculateFrameCRC(const PRS485_FRAME frame)
{
    if (frame == NULL) {
        return 0;
    }

    // Simplified CRC calculation - in real implementation, use proper CRC16
    UINT16 crc = 0xFFFF;
    const UINT8* data = (const UINT8*)frame;
    
    // Calculate CRC over header, ID, and payload (exclude CRC and trailer fields)
    for (UINT32 i = 0; i < (sizeof(RS485_FRAME) - sizeof(UINT16) - sizeof(UINT8)); i++) {
        crc ^= data[i];
        for (UINT32 j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = crc >> 1;
            }
        }
    }
    
    return crc;
}
