//
// RS485 Buffer Management Implementation
// Handles 12-byte payload buffer operations with FIFO guarantee
//

#include "RS485FilterDriver.h"

//
// Create Buffer Context
//
NTSTATUS RS485CreateBuffer(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ SIZE_T PayloadCount,
    _Out_ PRS485_BUFFER_CONTEXT* BufferContext
)
{
    NTSTATUS status;
    PRS485_BUFFER_CONTEXT buffer;
    WDF_OBJECT_ATTRIBUTES attributes;
    SIZE_T totalBufferSize;

    UNREFERENCED_PARAMETER(DeviceContext);

    if (BufferContext == NULL || PayloadCount == 0) {
        return STATUS_INVALID_PARAMETER;
    }

    // Allocate buffer context
    buffer = (PRS485_BUFFER_CONTEXT)ExAllocatePoolWithTag(NonPagedPool,
                                                          sizeof(RS485_BUFFER_CONTEXT),
                                                          RS485_POOL_TAG_BUFFER);
    if (buffer == NULL) {
        RS485_ERROR_PRINT("Failed to allocate buffer context");
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    RtlZeroMemory(buffer, sizeof(RS485_BUFFER_CONTEXT));

    // Initialize buffer properties
    buffer->Signature = RS485_BUFFER_SIGNATURE;
    buffer->Capacity = PayloadCount;
    buffer->PayloadSize = RS485_PAYLOAD_SIZE;
    buffer->Head = 0;
    buffer->Tail = 0;
    buffer->Count = 0;
    buffer->SequenceNumber = 0;

    // Allocate buffer memory
    totalBufferSize = PayloadCount * RS485_PAYLOAD_SIZE;
    buffer->Buffer = (UINT8*)ExAllocatePoolWithTag(NonPagedPool,
                                                   totalBufferSize,
                                                   RS485_POOL_TAG_BUFFER);
    if (buffer->Buffer == NULL) {
        RS485_ERROR_PRINT("Failed to allocate buffer memory: %zu bytes", totalBufferSize);
        ExFreePoolWithTag(buffer, RS485_POOL_TAG_BUFFER);
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    RtlZeroMemory(buffer->Buffer, totalBufferSize);

    // Create wait lock for synchronization
    WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
    status = WdfWaitLockCreate(&attributes, &buffer->Lock);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create buffer wait lock: 0x%x", status);
        ExFreePoolWithTag(buffer->Buffer, RS485_POOL_TAG_BUFFER);
        ExFreePoolWithTag(buffer, RS485_POOL_TAG_BUFFER);
        return status;
    }

    *BufferContext = buffer;

    RS485_DEBUG_PRINT("Buffer created: %zu payload slots, %zu bytes total",
                      PayloadCount, totalBufferSize);
    return STATUS_SUCCESS;
}

//
// Destroy Buffer Context
//
VOID RS485DestroyBuffer(
    _In_ PRS485_BUFFER_CONTEXT BufferContext
)
{
    if (BufferContext == NULL) {
        return;
    }

    // Validate buffer signature
    if (BufferContext->Signature != RS485_BUFFER_SIGNATURE) {
        RS485_ERROR_PRINT("Invalid buffer signature: 0x%x", BufferContext->Signature);
        return;
    }

    // Free buffer memory
    if (BufferContext->Buffer != NULL) {
        ExFreePoolWithTag(BufferContext->Buffer, RS485_POOL_TAG_BUFFER);
    }

    // Clear signature to prevent reuse
    BufferContext->Signature = 0;

    // Free buffer context
    ExFreePoolWithTag(BufferContext, RS485_POOL_TAG_BUFFER);

    RS485_DEBUG_PRINT("Buffer destroyed");
}

//
// Push Payload to Buffer (FIFO)
//
NTSTATUS RS485PushPayload(
    _In_ PRS485_BUFFER_CONTEXT BufferContext,
    _In_reads_bytes_(RS485_PAYLOAD_SIZE) const UINT8* PayloadData
)
{
    SIZE_T writeOffset;

    if (BufferContext == NULL || PayloadData == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Validate buffer signature
    if (BufferContext->Signature != RS485_BUFFER_SIGNATURE) {
        RS485_ERROR_PRINT("Invalid buffer signature: 0x%x", BufferContext->Signature);
        return STATUS_INVALID_PARAMETER;
    }

    // Acquire wait lock
    WdfWaitLockAcquire(BufferContext->Lock, NULL);

    // Check if buffer is full
    if (BufferContext->Count >= BufferContext->Capacity) {
        WdfWaitLockRelease(BufferContext->Lock);
        RS485_WARNING_PRINT("Buffer is full, cannot push payload");
        return STATUS_BUFFER_OVERFLOW;
    }

    // Calculate write offset
    writeOffset = BufferContext->Head * BufferContext->PayloadSize;

    // Copy payload data
    RtlCopyMemory(BufferContext->Buffer + writeOffset, PayloadData, RS485_PAYLOAD_SIZE);

    // Update head pointer (circular buffer)
    BufferContext->Head = (BufferContext->Head + 1) % BufferContext->Capacity;
    BufferContext->Count++;
    BufferContext->SequenceNumber++;

    // Release spinlock
    WdfSpinLockRelease(BufferContext->Lock, oldIrql);

    RS485_DEBUG_PRINT("Payload pushed to buffer: count=%zu/%zu, seq=%u",
                      BufferContext->Count, BufferContext->Capacity, BufferContext->SequenceNumber);
    return STATUS_SUCCESS;
}

//
// Pop Payload from Buffer (FIFO)
//
NTSTATUS RS485PopPayload(
    _In_ PRS485_BUFFER_CONTEXT BufferContext,
    _Out_writes_bytes_(RS485_PAYLOAD_SIZE) UINT8* PayloadData
)
{
    KIRQL oldIrql;
    SIZE_T readOffset;

    if (BufferContext == NULL || PayloadData == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Validate buffer signature
    if (BufferContext->Signature != RS485_BUFFER_SIGNATURE) {
        RS485_ERROR_PRINT("Invalid buffer signature: 0x%x", BufferContext->Signature);
        return STATUS_INVALID_PARAMETER;
    }

    // Acquire spinlock
    WdfSpinLockAcquire(BufferContext->Lock, &oldIrql);

    // Check if buffer is empty
    if (BufferContext->Count == 0) {
        WdfSpinLockRelease(BufferContext->Lock, oldIrql);
        return STATUS_BUFFER_UNDERFLOW;
    }

    // Calculate read offset
    readOffset = BufferContext->Tail * BufferContext->PayloadSize;

    // Copy payload data
    RtlCopyMemory(PayloadData, BufferContext->Buffer + readOffset, RS485_PAYLOAD_SIZE);

    // Update tail pointer (circular buffer)
    BufferContext->Tail = (BufferContext->Tail + 1) % BufferContext->Capacity;
    BufferContext->Count--;

    // Release spinlock
    WdfSpinLockRelease(BufferContext->Lock, oldIrql);

    RS485_DEBUG_PRINT("Payload popped from buffer: count=%zu/%zu",
                      BufferContext->Count, BufferContext->Capacity);
    return STATUS_SUCCESS;
}

//
// Check if Buffer is Full
//
BOOLEAN RS485IsBufferFull(
    _In_ PRS485_BUFFER_CONTEXT BufferContext
)
{
    KIRQL oldIrql;
    BOOLEAN isFull;

    if (BufferContext == NULL) {
        return TRUE; // Assume full if invalid
    }

    // Validate buffer signature
    if (BufferContext->Signature != RS485_BUFFER_SIGNATURE) {
        return TRUE; // Assume full if invalid
    }

    // Acquire spinlock
    WdfSpinLockAcquire(BufferContext->Lock, &oldIrql);
    isFull = (BufferContext->Count >= BufferContext->Capacity);
    WdfSpinLockRelease(BufferContext->Lock, oldIrql);

    return isFull;
}

//
// Check if Buffer is Empty
//
BOOLEAN RS485IsBufferEmpty(
    _In_ PRS485_BUFFER_CONTEXT BufferContext
)
{
    KIRQL oldIrql;
    BOOLEAN isEmpty;

    if (BufferContext == NULL) {
        return TRUE; // Assume empty if invalid
    }

    // Validate buffer signature
    if (BufferContext->Signature != RS485_BUFFER_SIGNATURE) {
        return TRUE; // Assume empty if invalid
    }

    // Acquire spinlock
    WdfSpinLockAcquire(BufferContext->Lock, &oldIrql);
    isEmpty = (BufferContext->Count == 0);
    WdfSpinLockRelease(BufferContext->Lock, oldIrql);

    return isEmpty;
}

//
// Clear Buffer
//
VOID RS485ClearBuffer(
    _In_ PRS485_BUFFER_CONTEXT BufferContext
)
{
    KIRQL oldIrql;

    if (BufferContext == NULL) {
        return;
    }

    // Validate buffer signature
    if (BufferContext->Signature != RS485_BUFFER_SIGNATURE) {
        RS485_ERROR_PRINT("Invalid buffer signature: 0x%x", BufferContext->Signature);
        return;
    }

    // Acquire spinlock
    WdfSpinLockAcquire(BufferContext->Lock, &oldIrql);

    // Reset buffer pointers and count
    BufferContext->Head = 0;
    BufferContext->Tail = 0;
    BufferContext->Count = 0;

    // Clear buffer memory
    RtlZeroMemory(BufferContext->Buffer, BufferContext->Capacity * BufferContext->PayloadSize);

    // Release spinlock
    WdfSpinLockRelease(BufferContext->Lock, oldIrql);

    RS485_DEBUG_PRINT("Buffer cleared");
}

//
// Get Buffer Used Count
//
SIZE_T RS485GetBufferUsedCount(
    _In_ PRS485_BUFFER_CONTEXT BufferContext
)
{
    KIRQL oldIrql;
    SIZE_T count;

    if (BufferContext == NULL) {
        return 0;
    }

    // Validate buffer signature
    if (BufferContext->Signature != RS485_BUFFER_SIGNATURE) {
        return 0;
    }

    // Acquire spinlock
    WdfSpinLockAcquire(BufferContext->Lock, &oldIrql);
    count = BufferContext->Count;
    WdfSpinLockRelease(BufferContext->Lock, oldIrql);

    return count;
}

//
// Get Buffer Total Count
//
SIZE_T RS485GetBufferTotalCount(
    _In_ PRS485_BUFFER_CONTEXT BufferContext
)
{
    if (BufferContext == NULL) {
        return 0;
    }

    // Validate buffer signature
    if (BufferContext->Signature != RS485_BUFFER_SIGNATURE) {
        return 0;
    }

    return BufferContext->Capacity;
}

//
// Check Buffer Flags
//
NTSTATUS RS485CheckBufferFlags(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ RS485_BUFFER_TYPE BufferType,
    _Out_ PBOOLEAN IsBufferFull
)
{
    if (DeviceContext == NULL || IsBufferFull == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    switch (BufferType) {
        case BufferTypeUplink:
            *IsBufferFull = RS485IsBufferFull(DeviceContext->UplinkBuffer);
            break;

        case BufferTypeDownlink:
            *IsBufferFull = RS485IsBufferFull(DeviceContext->DownlinkBuffer);
            break;

        case BufferTypeBoth:
            *IsBufferFull = RS485IsBufferFull(DeviceContext->UplinkBuffer) ||
                           RS485IsBufferFull(DeviceContext->DownlinkBuffer);
            break;

        default:
            return STATUS_INVALID_PARAMETER;
    }

    return STATUS_SUCCESS;
}

//
// Update Buffer Flags
//
VOID RS485UpdateBufferFlags(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext
)
{
    if (DeviceContext == NULL) {
        return;
    }

    // Update uplink buffer flag
    DeviceContext->UplinkBufferFull = RS485IsBufferFull(DeviceContext->UplinkBuffer);

    // Update downlink buffer flag
    DeviceContext->DownlinkBufferFull = RS485IsBufferFull(DeviceContext->DownlinkBuffer);

    RS485_DEBUG_PRINT("Buffer flags updated: Uplink=%s, Downlink=%s",
                      DeviceContext->UplinkBufferFull ? "FULL" : "OK",
                      DeviceContext->DownlinkBufferFull ? "FULL" : "OK");
}
