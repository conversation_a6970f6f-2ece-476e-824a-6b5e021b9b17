;
; AI-SLDAP RS485 Driver with FTDI Integration
; Windows User-Mode Driver Framework (UMDF) 2.0 Implementation
; Integrates with FTDI VCP Driver for complete RS485 solution
;

[Version]
Signature="$WINDOWS NT$"
Class=Ports
ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
Provider=%ManufacturerName%
CatalogFile=RS485FilterDriver.cat
DriverVer=07/21/2025,1.0.0.0
PnpLockdown=1

[Manufacturer]
%ManufacturerName%=Standard,NTamd64,NTx86

[Standard.NTamd64]
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6001
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6015
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6010
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6011

[Standard.NTx86]
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6001
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6015
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6010
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6011

[RS485Filter_Install.NT]
CopyFiles=UMDriverCopy
Include=winusb.inf
Needs=WINUSB.NT

[RS485Filter_Install.NT.Services]
Include=winusb.inf
AddService=WinUSB,0x00000002,WinUSB_ServiceInstall
AddService=RS485FilterDriver,0x00000000,RS485Filter_ServiceInstall

[RS485Filter_ServiceInstall]
DisplayName=%RS485Service.DisplayName%
ServiceType=1
StartType=3
ErrorControl=1
ServiceBinary=%12%\RS485FilterDriver.sys

[WinUSB_ServiceInstall]
DisplayName=%WinUSB_SvcDesc%
ServiceType=1
StartType=3
ErrorControl=1
ServiceBinary=%12%\WinUSB.sys

[RS485Filter_Install.NT.Wdf]
UmdfService=RS485FilterDriver,RS485Filter_Install_UmdfService
UmdfServiceOrder=RS485FilterDriver
UmdfKernelModeClientPolicy=AllowKernelModeClients
UmdfFileObjectPolicy=AllowNullAndUnknownFileObjects
UmdfFsContextUsePolicy=CanUseFsContext2

[RS485Filter_Install_UmdfService]
UmdfLibraryVersion=2.15.0
ServiceBinary=%12%\UMDF\RS485FilterDriver.dll
UmdfDispatcher=FileHandle
UmdfImpersonationLevel=Impersonation

[RS485Filter_Install.NT.HW]
AddReg=RS485Filter_AddReg

[RS485Filter_AddReg]
HKR,,"DeviceInterfaceGUIDs",0x00010000,"{A5DCBF10-6530-11D2-901F-00C04FB951ED}"
HKR,,"System.Devices.InterfaceClassGuid",0x00000000,"{A5DCBF10-6530-11D2-901F-00C04FB951ED}"

[UMDriverCopy]
RS485FilterDriver.dll

[DestinationDirs]
UMDriverCopy=12,UMDF

[Strings]
ManufacturerName="AI-SLDAP Technologies"
RS485Device.DeviceDesc="AI-SLDAP RS485 Communication Driver"
RS485Service.DisplayName="AI-SLDAP RS485 Filter Service"
WinUSB_SvcDesc="WinUSB Driver"
DiskName="AI-SLDAP RS485 Driver Installation Disk"
