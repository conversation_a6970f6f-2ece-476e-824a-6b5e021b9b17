#ifndef RS485_PROTOCOL_H
#define RS485_PROTOCOL_H

//
// RS485 Protocol Structures and Definitions
// ZES Protocol Implementation for UMDF Driver
//

#include "RS485Common.h"

//
// ZES Protocol Frame Structure (16 bytes total)
//
#pragma pack(push, 1)
typedef struct _RS485_FRAME {
    UINT8   Header;         // 0xAA - Frame start marker
    UINT8   IdByte;         // Function code (3 bits) + Device address (5 bits)
    UINT8   Payload[12];    // Key (4 bytes) + Value (8 bytes) - CORE PROTOCOL DATA
    UINT8   Crc8;           // CRC8 checksum for error detection
    UINT8   Trailer;        // 0x0D - Frame end marker
} RS485_FRAME, *PRS485_FRAME;
#pragma pack(pop)

//
// Payload Structure (12 bytes - the heart of the protocol)
//
#pragma pack(push, 1)
typedef struct _RS485_PAYLOAD {
    UINT8   Key[4];         // Command identifier (ASCII string, e.g., "S001", "U001", "A001")
    UINT8   Value[8];       // Data value (integers, floats, or structured data in little-endian)
} RS485_PAYLOAD, *PRS485_PAYLOAD;
#pragma pack(pop)

//
// Frame Processing States
//
typedef enum _RS485_FRAME_STATE {
    FrameStateWaitingHeader = 0,    // Looking for 0xAA
    FrameStateReadingId,            // Reading ID byte (contains function code)
    FrameStateReadingPayload,       // Reading 12-byte payload
    FrameStateReadingCrc,           // Reading CRC8
    FrameStateReadingTrailer,       // Looking for 0x0D
    FrameStateComplete,             // Frame ready for processing
    FrameStateError                 // CRC or format error
} RS485_FRAME_STATE, *PRS485_FRAME_STATE;

//
// Frame Processing Results
//
typedef enum _RS485_FRAME_PROCESS_RESULT {
    FrameProcessContinue = 0,       // Continue processing
    FrameProcessFrameReady,         // Complete frame ready
    FrameProcessFrameError,         // Frame error detected
    FrameProcessTimeout             // Frame timeout
} RS485_FRAME_PROCESS_RESULT;

//
// Buffer Types
//
typedef enum _RS485_BUFFER_TYPE {
    BufferTypeUplink = 0,           // PC to device buffer
    BufferTypeDownlink,             // Device to PC buffer
    BufferTypeBoth                  // Both buffers
} RS485_BUFFER_TYPE;

//
// Buffer Overflow Policies
//
typedef enum _RS485_BUFFER_OVERFLOW_POLICY {
    BufferOverflowTriggerError = 0, // Return error when buffer is full (RECOMMENDED)
    BufferOverflowDiscardOldest,    // Discard oldest frame when buffer is full
    BufferOverflowDiscardNewest     // Discard new frame when buffer is full
} RS485_BUFFER_OVERFLOW_POLICY;

//
// Buffer Status Structure
//
typedef struct _RS485_BUFFER_STATUS {
    UINT32  UplinkUsed;             // Used payload slots in uplink buffer (0-5)
    UINT32  UplinkTotal;            // Total uplink buffer capacity (5 payload slots)
    UINT32  DownlinkUsed;           // Used payload slots in downlink buffer (0-10)
    UINT32  DownlinkTotal;          // Total downlink buffer capacity (10 payload slots)
    UINT32  PayloadSize;            // Size per payload slot (12 bytes)
    BOOLEAN IsUplinkFull;           // Uplink buffer full flag
    BOOLEAN IsDownlinkFull;         // Downlink buffer full flag
    BOOLEAN IsOverflowDetected;     // Buffer overflow status
    UINT32  TotalBufferBytes;       // Total buffer capacity in bytes (60 + 120 = 180 bytes)
} RS485_BUFFER_STATUS, *PRS485_BUFFER_STATUS;

//
// Hardware Status Structure
//
typedef struct _RS485_HARDWARE_STATUS {
    BOOLEAN IsConnected;            // Connection status
    UINT32  FtdiChipStatus;         // FTDI chip status flags
    UINT32  BufferOverflows;        // Number of buffer overflow events
    UINT32  CrcErrors;              // Total CRC errors detected
    UINT32  TimeoutErrors;          // Total timeout errors
    DOUBLE  SignalStrength;         // RS485 signal strength (if available)
    UINT32  FramesSent;             // Total frames transmitted
    UINT32  FramesReceived;         // Total frames received successfully
} RS485_HARDWARE_STATUS, *PRS485_HARDWARE_STATUS;

//
// Performance Metrics Structure
//
typedef struct _RS485_PERFORMANCE_METRICS {
    DOUBLE  AvgLatencyMs;           // Average response latency in milliseconds
    UINT32  BytesPerSecond;         // Throughput in bytes per second (payload data only)
    UINT32  SuccessfulFrames;       // Number of successful frame transmissions
    UINT32  FailedFrames;           // Number of failed frame transmissions
    UINT32  RetryCount;             // Total number of retries performed
    DOUBLE  FrameSuccessRate;       // Success rate as percentage (0-100)
    DOUBLE  AvgBusUtilization;      // Average bus utilization percentage (0-100)
    UINT32  MaxResponseTimeMs;      // Maximum response time observed
    UINT32  CrcErrorCount;          // Total CRC errors detected
    UINT32  TimeoutCount;           // Total timeout errors encountered
    UINT32  BufferOverflowCount;    // Number of buffer overflow events
} RS485_PERFORMANCE_METRICS, *PRS485_PERFORMANCE_METRICS;

//
// Line Status Structure
//
typedef struct _RS485_LINE_STATUS {
    BOOLEAN CarrierDetect;          // CD - Carrier Detect signal status
    BOOLEAN DataSetReady;           // DSR - Data Set Ready signal status
    BOOLEAN RingIndicator;          // RI - Ring Indicator signal status
    BOOLEAN ClearToSend;            // CTS - Clear To Send signal status
    BOOLEAN DataTerminalReady;      // DTR - Data Terminal Ready signal status
    BOOLEAN RequestToSend;          // RTS - Request To Send signal status
    UINT32  SignalStrength;         // Signal strength indicator (0-100%)
    BOOLEAN BusIdle;                // True if RS485 bus is currently idle
    UINT32  NoiseLevel;             // Background noise level (0-100%)
    BOOLEAN HardwareError;          // Hardware-level error detected
    UINT32  LastActivityMs;         // Milliseconds since last bus activity
} RS485_LINE_STATUS, *PRS485_LINE_STATUS;

//
// IOCTL Input/Output Structures
//

// System Configuration Input
typedef struct _RS485_SYSTEM_CONFIG_INPUT {
    CHAR    CommandKey[4];          // Command key (e.g., "S001", "S002")
    UINT64  Value;                  // Configuration value
    UINT8   FunctionCode;           // Function code (0b111 for assign data)
    UINT8   SlaveAddress;           // Target slave address (0x00 for broadcast)
    UINT8   Reserved[2];            // Padding for alignment
} RS485_SYSTEM_CONFIG_INPUT, *PRS485_SYSTEM_CONFIG_INPUT;

// User Configuration Input
typedef struct _RS485_USER_CONFIG_INPUT {
    CHAR    CommandKey[4];          // Command key (e.g., "U001", "U002")
    UINT64  Value;                  // Configuration value
    UINT8   SlaveAddress;           // Target slave address (from S001)
    UINT8   Reserved[3];            // Padding for alignment
} RS485_USER_CONFIG_INPUT, *PRS485_USER_CONFIG_INPUT;

// Data Request Input
typedef struct _RS485_DATA_REQUEST_INPUT {
    CHAR    DataKey[4];             // Data key (e.g., "A001", "A002")
    UINT8   SlaveAddress;           // Target slave address (from S001)
    UINT8   Reserved[3];            // Padding for alignment
} RS485_DATA_REQUEST_INPUT, *PRS485_DATA_REQUEST_INPUT;

// Response Receive Input
typedef struct _RS485_RESPONSE_RECEIVE_INPUT {
    UINT8   SlaveAddress;           // Expected slave address
    UINT32  TimeoutMs;              // Timeout in milliseconds
    UINT8   Reserved[3];            // Padding for alignment
} RS485_RESPONSE_RECEIVE_INPUT, *PRS485_RESPONSE_RECEIVE_INPUT;

// Response Receive Output
typedef struct _RS485_RESPONSE_RECEIVE_OUTPUT {
    UINT8   ResponseData[12];       // 12-byte payload data
    UINT8   ActualSlaveAddress;     // Actual responding slave address
    BOOLEAN IsDataReady;            // Whether data is ready
    UINT8   Reserved[2];            // Padding for alignment
} RS485_RESPONSE_RECEIVE_OUTPUT, *PRS485_RESPONSE_RECEIVE_OUTPUT;

// Model Data Operation Input
typedef struct _RS485_MODEL_DATA_INPUT {
    UINT8   SlaveAddress;           // Target slave address
    UINT32  Address;                // Memory address in FRAM
    UINT8   Data[12];               // Data to write (for write operations)
    BOOLEAN IsWrite;                // TRUE for write, FALSE for read
    UINT32  Length;                 // Length of data (for read operations)
    UINT8   Reserved[3];            // Padding for alignment
} RS485_MODEL_DATA_INPUT, *PRS485_MODEL_DATA_INPUT;

//
// Command Key Validation Macros
//
#define RS485_IS_SYSTEM_COMMAND(key) \
    (strncmp((key), "S001", 4) == 0 || strncmp((key), "S002", 4) == 0)

#define RS485_IS_USER_COMMAND(key) \
    (strncmp((key), "U001", 4) == 0 || strncmp((key), "U002", 4) == 0 || \
     strncmp((key), "U003", 4) == 0 || strncmp((key), "U004", 4) == 0 || \
     strncmp((key), "U005", 4) == 0 || strncmp((key), "U006", 4) == 0)

#define RS485_IS_APPLICATION_COMMAND(key) \
    (strncmp((key), "A001", 4) == 0 || strncmp((key), "A002", 4) == 0 || \
     strncmp((key), "A003", 4) == 0 || strncmp((key), "A004", 4) == 0 || \
     strncmp((key), "A005", 4) == 0)

#define RS485_IS_MODEL_COMMAND(key) \
    (strncmp((key), "W001", 4) == 0 || strncmp((key), "W002", 4) == 0)

//
// CRC8 Calculation Functions
//
UINT8 RS485CalculateCRC8(_In_reads_bytes_(Length) const UINT8* Data, _In_ SIZE_T Length);
BOOLEAN RS485VerifyCRC8(_In_ const RS485_FRAME* Frame);

//
// Frame Processing Functions
//
VOID RS485InitializeFrameProcessor(_Out_ PRS485_FRAME_STATE State);
RS485_FRAME_PROCESS_RESULT RS485ProcessIncomingByte(_Inout_ PRS485_FRAME_STATE State, 
                                                   _In_ UINT8 Byte,
                                                   _Inout_ PRS485_FRAME Frame,
                                                   _Inout_ PSIZE_T BytesReceived);

//
// Payload Data Extraction and Storage Functions
//
VOID RS485ExtractPayloadKey(_In_ const RS485_PAYLOAD* Payload, _Out_writes_(5) PCHAR Key);
UINT32 RS485ExtractPayloadInteger(_In_ const RS485_PAYLOAD* Payload);
FLOAT RS485ExtractPayloadFloat(_In_ const RS485_PAYLOAD* Payload);
DOUBLE RS485ExtractPayloadDouble(_In_ const RS485_PAYLOAD* Payload);
VOID RS485ExtractPayloadDualIntegers(_In_ const RS485_PAYLOAD* Payload, 
                                    _Out_ PUINT32 Value1, 
                                    _Out_ PUINT32 Value2);

VOID RS485StorePayloadKey(_Out_ PRS485_PAYLOAD Payload, _In_z_ PCSTR Key);
VOID RS485StorePayloadInteger(_Out_ PRS485_PAYLOAD Payload, _In_ UINT32 Value);
VOID RS485StorePayloadFloat(_Out_ PRS485_PAYLOAD Payload, _In_ FLOAT Value);
VOID RS485StorePayloadDouble(_Out_ PRS485_PAYLOAD Payload, _In_ DOUBLE Value);
VOID RS485StorePayloadDualIntegers(_Out_ PRS485_PAYLOAD Payload, 
                                  _In_ UINT32 Value1, 
                                  _In_ UINT32 Value2);

#endif // RS485_PROTOCOL_H
