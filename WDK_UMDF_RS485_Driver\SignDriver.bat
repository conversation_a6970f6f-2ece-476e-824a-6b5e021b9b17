@echo off
REM ===================================================================
REM RS485 Driver Signing Script
REM Creates catalog file and signs driver for proper installation
REM ===================================================================

echo ===================================================================
echo RS485 Driver Signing Tool
echo ===================================================================
echo.
echo This script will:
echo 1. Create catalog file for the driver
echo 2. Create test certificate (for development)
echo 3. Sign the driver files
echo 4. Enable test signing mode
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Set paths
set DRIVER_DIR=Driver
set BUILD_DIR=Driver\Build\Release\x64
set OUTPUT_DIR=FinalOutput
set INF_FILE=%DRIVER_DIR%\RS485FilterDriver.inf
set DLL_FILE=%BUILD_DIR%\RS485FilterDriver.dll

echo Step 1: Creating catalog file...
echo ===================================================================

REM Check if inf2cat is available
where inf2cat >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: inf2cat not found!
    echo Make sure Windows Driver Kit (WDK) is installed and in your PATH.
    echo Typically located at: C:\Program Files (x86)\Windows Kits\10\bin\x86
    pause
    exit /b 1
)

REM Create catalog file
echo Creating catalog file from INF...
inf2cat /driver:%DRIVER_DIR% /os:10_X64
if %errorLevel% neq 0 (
    echo WARNING: Failed to create catalog file.
    echo This may be due to INF file issues or missing inf2cat tool.
    echo.
) else (
    echo Catalog file created successfully.
    echo.
)

echo Step 2: Creating test certificate...
echo ===================================================================

REM Create test certificate if it doesn't exist
makecert -r -pe -ss PrivateCertStore -n "CN=RS485TestCert" -eku 1.3.6.1.5.5.7.3.3 RS485TestCert.cer
if %errorLevel% neq 0 (
    echo WARNING: makecert failed. Trying alternative method...

    REM Try using PowerShell to create certificate
    powershell -Command "New-SelfSignedCertificate -Subject 'CN=RS485TestCert' -CertStoreLocation 'Cert:\CurrentUser\My' -KeyUsage DigitalSignature -Type CodeSigningCert"
)

echo.
echo Step 3: Signing driver files...
echo ===================================================================

REM Sign the driver DLL
echo Signing driver DLL...
signtool sign /v /s PrivateCertStore /n "RS485TestCert" /t http://timestamp.digicert.com "%DLL_FILE%"

REM Sign the catalog file if it exists
if exist "%DRIVER_DIR%\RS485FilterDriver.cat" (
    echo Signing catalog file...
    signtool sign /v /s PrivateCertStore /n "RS485TestCert" /t http://timestamp.digicert.com "%DRIVER_DIR%\RS485FilterDriver.cat"
)

echo.
echo Step 4: Installing test certificate...
echo ===================================================================

REM Install certificate to trusted root (for development only)
certmgr -add RS485TestCert.cer -s -r localMachine root
certmgr -add RS485TestCert.cer -s -r localMachine trustedpublisher

echo.
echo Step 5: Enabling test signing mode...
echo ===================================================================

REM Enable test signing
bcdedit /set testsigning on
if %errorLevel% equ 0 (
    echo Test signing enabled successfully.
    echo IMPORTANT: You must REBOOT your computer for changes to take effect!
) else (
    echo WARNING: Failed to enable test signing. You may need to disable Secure Boot.
)

echo.
echo Step 6: Copying signed files to output directory...
echo ===================================================================

REM Copy signed files to output
copy "%DLL_FILE%" "%OUTPUT_DIR%\" /Y
copy "%DRIVER_DIR%\RS485FilterDriver.inf" "%OUTPUT_DIR%\" /Y
if exist "%DRIVER_DIR%\RS485FilterDriver.cat" (
    copy "%DRIVER_DIR%\RS485FilterDriver.cat" "%OUTPUT_DIR%\" /Y
)

echo.
echo ===================================================================
echo Signing Complete
echo ===================================================================
echo.
echo Next steps:
echo 1. REBOOT your computer to enable test signing
echo 2. After reboot, run the installer: .\FinalOutput\RS485DriverInstaller.exe
echo.
echo NOTE: For production deployment, use a proper code signing certificate
echo instead of the test certificate created by this script.
echo.

pause
