@echo off
REM ===================================================================
REM RS485 Driver Signing Script for Development
REM Creates test certificate and signs the driver for development use
REM ===================================================================

echo ===================================================================
echo RS485 Driver Development Signing
echo ===================================================================
echo.
echo This script will:
echo 1. Create a test certificate for development
echo 2. Sign the RS485 driver DLL and INF
echo 3. Enable test signing mode (requires reboot)
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Step 1: Creating test certificate...
echo ===================================================================

REM Create test certificate if it doesn't exist
makecert -r -pe -ss PrivateCertStore -n "CN=RS485TestCert" -eku *******.*******.3 RS485TestCert.cer
if %errorLevel% neq 0 (
    echo WARNING: makecert failed. Trying alternative method...
    
    REM Try using PowerShell to create certificate
    powershell -Command "New-SelfSignedCertificate -Subject 'CN=RS485TestCert' -CertStoreLocation 'Cert:\CurrentUser\My' -KeyUsage DigitalSignature -Type CodeSigningCert"
)

echo.
echo Step 2: Signing driver files...
echo ===================================================================

REM Sign the driver DLL
signtool sign /v /s PrivateCertStore /n "RS485TestCert" /t http://timestamp.digicert.com "Driver\Build\Release\x64\RS485FilterDriver.dll"

REM Sign the INF file
signtool sign /v /s PrivateCertStore /n "RS485TestCert" /t http://timestamp.digicert.com "Driver\RS485FilterDriver.inf"

echo.
echo Step 3: Installing test certificate...
echo ===================================================================

REM Install certificate to trusted root (for development only)
certmgr -add RS485TestCert.cer -s -r localMachine root
certmgr -add RS485TestCert.cer -s -r localMachine trustedpublisher

echo.
echo Step 4: Enabling test signing mode...
echo ===================================================================

REM Enable test signing
bcdedit /set testsigning on
if %errorLevel% equ 0 (
    echo Test signing enabled successfully.
    echo IMPORTANT: You must REBOOT your computer for changes to take effect!
) else (
    echo WARNING: Failed to enable test signing. You may need to disable Secure Boot.
)

echo.
echo ===================================================================
echo Signing Complete
echo ===================================================================
echo.
echo Next steps:
echo 1. REBOOT your computer to enable test signing
echo 2. After reboot, run BuildAll.bat to rebuild with signed drivers
echo 3. Test the installer
echo.
echo NOTE: This is for DEVELOPMENT ONLY!
echo For production, use a proper code signing certificate.
echo.

pause
