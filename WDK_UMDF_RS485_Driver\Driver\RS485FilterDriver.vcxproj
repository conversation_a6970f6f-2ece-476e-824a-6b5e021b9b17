<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{12345678-1234-1234-1234-123456789ABC}</ProjectGuid>
    <TemplateGuid>{32909489-7be5-497b-aafa-db6669d9b44b}</TemplateGuid>
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <DriverType>UMDF</DriverType>
    <DriverTargetPlatform>Universal</DriverTargetPlatform>
    <Inf2CatUseLocalTime>true</Inf2CatUseLocalTime>
    <SpectreMitigation>false</SpectreMitigation>
    <UMDF_VERSION_MAJOR>2</UMDF_VERSION_MAJOR>
    <Platform Condition="'$(Platform)' == ''">Win32</Platform>
    <RootNamespace>RS485FilterDriver</RootNamespace>
    <WindowsTargetPlatformVersion>$(LatestTargetPlatformVersion)</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <DebuggerFlavor>DbgengRemoteDebugger</DebuggerFlavor>
    <OutDir>$(SolutionDir)Build\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>$(SolutionDir)Build\Intermediate\$(ProjectName)\$(Configuration)\$(Platform)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <DebuggerFlavor>DbgengRemoteDebugger</DebuggerFlavor>
    <OutDir>$(SolutionDir)Build\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>$(SolutionDir)Build\Intermediate\$(ProjectName)\$(Configuration)\$(Platform)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DebuggerFlavor>DbgengRemoteDebugger</DebuggerFlavor>
    <OutDir>$(SolutionDir)Build\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>$(SolutionDir)Build\Intermediate\$(ProjectName)\$(Configuration)\$(Platform)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <DebuggerFlavor>DbgengRemoteDebugger</DebuggerFlavor>
    <OutDir>$(SolutionDir)Build\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>$(SolutionDir)Build\Intermediate\$(ProjectName)\$(Configuration)\$(Platform)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WppEnabled>true</WppEnabled>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData>trace.h</WppScanConfigurationData>
      <AdditionalIncludeDirectories>$(SolutionDir)Include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um;C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared;C:\Program Files (x86)\Windows Kits\10\Include\wdf\umdf\2.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;UNICODE;_UNICODE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>%(AdditionalDependencies);C:\Program Files (x86)\Windows Kits\10\Lib\wdf\umdf\x64\2.0\WdfDriverStubUm.lib</AdditionalDependencies>
      <ModuleDefinitionFile>RS485FilterDriver.def</ModuleDefinitionFile>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(ProjectDir)RS485FilterDriver.inf" "$(OutDir)"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WppEnabled>true</WppEnabled>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData>trace.h</WppScanConfigurationData>
      <AdditionalIncludeDirectories>$(SolutionDir)Include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um;C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared;C:\Program Files (x86)\Windows Kits\10\Include\wdf\umdf\2.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>UNICODE;_UNICODE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>%(AdditionalDependencies);C:\Program Files (x86)\Windows Kits\10\Lib\wdf\umdf\x64\2.0\WdfDriverStubUm.lib</AdditionalDependencies>
      <ModuleDefinitionFile>RS485FilterDriver.def</ModuleDefinitionFile>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(ProjectDir)RS485FilterDriver.inf" "$(OutDir)"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WppEnabled>true</WppEnabled>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData>trace.h</WppScanConfigurationData>
      <AdditionalIncludeDirectories>$(SolutionDir)Include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um;C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared;C:\Program Files (x86)\Windows Kits\10\Include\wdf\umdf\2.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;UNICODE;_UNICODE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>%(AdditionalDependencies);C:\Program Files (x86)\Windows Kits\10\Lib\wdf\umdf\x64\2.0\WdfDriverStubUm.lib</AdditionalDependencies>
      <ModuleDefinitionFile>RS485FilterDriver.def</ModuleDefinitionFile>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(ProjectDir)RS485FilterDriver.inf" "$(OutDir)"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WppEnabled>true</WppEnabled>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData>trace.h</WppScanConfigurationData>
      <AdditionalIncludeDirectories>$(SolutionDir)Include;C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um;C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared;C:\Program Files (x86)\Windows Kits\10\Include\wdf\umdf\2.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>UNICODE;_UNICODE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>%(AdditionalDependencies);C:\Program Files (x86)\Windows Kits\10\Lib\wdf\umdf\x64\2.0\WdfDriverStubUm.lib</AdditionalDependencies>
      <ModuleDefinitionFile>RS485FilterDriver.def</ModuleDefinitionFile>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(ProjectDir)RS485FilterDriver.inf" "$(OutDir)"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <FilesToPackage Include="$(TargetPath)" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="RS485Buffer.cpp" />
    <ClCompile Include="RS485Device.cpp" />
    <ClCompile Include="RS485FilterDriver.cpp" />
    <ClCompile Include="RS485Protocol.cpp" />
    <ClCompile Include="RS485Queue.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\Include\RS485Common.h" />
    <ClInclude Include="..\Include\RS485Errors.h" />
    <ClInclude Include="..\Include\RS485Protocol.h" />
    <ClInclude Include="RS485FilterDriver.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="RS485FilterDriver.def" />
  </ItemGroup>
  <ItemGroup>
    <Inf Include="RS485FilterDriver.inf" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
