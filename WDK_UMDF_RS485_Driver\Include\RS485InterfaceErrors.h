#ifndef RS485_INTERFACE_ERRORS_H
#define RS485_INTERFACE_ERRORS_H

//
// RS485 Driver Interface Error Codes and Result Types
// Error handling for user-mode interface implementation
//

#include "RS485InterfaceCommon.h"

//
// Main RS485 Error Enumeration
//
typedef enum _RS485_ERROR {
    // Success code
    RS485_SUCCESS = 0,

    // General errors (1-99)
    RS485_ERROR_INVALID_PARAMETER = 1,
    RS485_ERROR_INSUFFICIENT_RESOURCES = 2,
    RS485_ERROR_DEVICE_NOT_FOUND = 3,
    RS485_ERROR_DEVICE_NOT_READY = 4,
    RS485_ERROR_OPERATION_FAILED = 5,
    RS485_ERROR_TIMEOUT = 6,
    RS485_ERROR_CANCELLED = 7,
    RS485_ERROR_ACCESS_DENIED = 8,
    RS485_ERROR_DEVICE_BUSY = 9,
    RS485_ERROR_NOT_SUPPORTED = 10,

    // Communication errors (100-199)
    RS485_ERROR_COMMUNICATION_FAILED = 100,
    RS485_ERROR_FRAME_FORMAT = 101,
    RS485_ERROR_CRC_MISMATCH = 102,
    RS485_ERROR_RESPONSE_TIMEOUT = 103,
    RS485_ERROR_UNEXPECTED_RESPONSE = 104,
    RS485_ERROR_TRANSMISSION_FAILED = 105,
    RS485_ERROR_RECEPTION_FAILED = 106,
    RS485_ERROR_PROTOCOL_VIOLATION = 107,
    RS485_ERROR_SLAVE_NO_RESPONSE = 108,
    RS485_ERROR_SLAVE_ERROR_RESPONSE = 109,

    // Buffer errors (200-299)
    RS485_ERROR_BUFFER_OVERFLOW = 200,
    RS485_ERROR_BUFFER_UNDERFLOW = 201,
    RS485_ERROR_BUFFER_FULL = 202,
    RS485_ERROR_BUFFER_EMPTY = 203,
    RS485_ERROR_BUFFER_CORRUPTED = 204,
    RS485_ERROR_BUFFER_SIZE_MISMATCH = 205,
    RS485_ERROR_QUEUE_FULL = 206,
    RS485_ERROR_QUEUE_EMPTY = 207,

    // Configuration errors (300-399)
    RS485_ERROR_INVALID_BAUD_RATE = 300,
    RS485_ERROR_INVALID_SLAVE_ADDRESS = 301,
    RS485_ERROR_INVALID_COMMAND = 302,
    RS485_ERROR_INVALID_DATA_FORMAT = 303,
    RS485_ERROR_CONFIGURATION_LOCKED = 304,
    RS485_ERROR_CALIBRATION_REQUIRED = 305,
    RS485_ERROR_FACTORY_RESET_REQUIRED = 306,

    // Hardware errors (400-499)
    RS485_ERROR_HARDWARE_FAILURE = 400,
    RS485_ERROR_SENSOR_MALFUNCTION = 401,
    RS485_ERROR_POWER_SUPPLY_ERROR = 402,
    RS485_ERROR_TEMPERATURE_OUT_OF_RANGE = 403,
    RS485_ERROR_VOLTAGE_OUT_OF_RANGE = 404,
    RS485_ERROR_CURRENT_OUT_OF_RANGE = 405,
    RS485_ERROR_MEMORY_CORRUPTION = 406,
    RS485_ERROR_EEPROM_FAILURE = 407,

    // Driver-specific errors (500-599)
    RS485_ERROR_DRIVER_NOT_LOADED = 500,
    RS485_ERROR_DRIVER_VERSION_MISMATCH = 501,
    RS485_ERROR_DEVICE_HANDLE_INVALID = 502,
    RS485_ERROR_IOCTL_FAILED = 503,
    RS485_ERROR_DRIVER_INTERNAL_ERROR = 504,
    RS485_ERROR_SYNCHRONIZATION_FAILED = 505,
    RS485_ERROR_THREAD_CREATION_FAILED = 506,

    // Application errors (600-699)
    RS485_ERROR_INITIALIZATION_FAILED = 600,
    RS485_ERROR_ALREADY_INITIALIZED = 601,
    RS485_ERROR_NOT_INITIALIZED = 602,
    RS485_ERROR_SHUTDOWN_IN_PROGRESS = 603,
    RS485_ERROR_OPERATION_IN_PROGRESS = 604,
    RS485_ERROR_CALLBACK_REGISTRATION_FAILED = 605,

    // Maximum error code
    RS485_ERROR_MAX = 999
} RS485_ERROR;

//
// Error Severity Levels
//
typedef enum _RS485_ERROR_SEVERITY {
    RS485_SEVERITY_INFO = 0,        // Informational message
    RS485_SEVERITY_WARNING = 1,     // Warning condition
    RS485_SEVERITY_ERROR = 2,       // Error condition
    RS485_SEVERITY_CRITICAL = 3     // Critical error
} RS485_ERROR_SEVERITY;

//
// Error Information Structure
//
typedef struct _RS485_ERROR_INFO {
    RS485_ERROR         ErrorCode;      // Error code
    RS485_ERROR_SEVERITY Severity;      // Error severity
    UINT32              LineNumber;     // Source line number (debug builds)
    CHAR                FileName[64];   // Source file name (debug builds)
    CHAR                FunctionName[64]; // Function name (debug builds)
    CHAR                Description[256]; // Error description
    LARGE_INTEGER       Timestamp;     // Error timestamp
} RS485_ERROR_INFO, *PRS485_ERROR_INFO;

//
// Error Statistics Structure
//
typedef struct _RS485_ERROR_STATISTICS {
    UINT32  TotalErrors;            // Total error count
    UINT32  CommunicationErrors;    // Communication error count
    UINT32  BufferErrors;           // Buffer error count
    UINT32  ConfigurationErrors;    // Configuration error count
    UINT32  HardwareErrors;         // Hardware error count
    UINT32  DriverErrors;           // Driver error count
    UINT32  ApplicationErrors;      // Application error count
    UINT32  CriticalErrors;         // Critical error count
    LARGE_INTEGER LastErrorTime;   // Timestamp of last error
    RS485_ERROR LastErrorCode;      // Last error code
} RS485_ERROR_STATISTICS, *PRS485_ERROR_STATISTICS;

//
// Result Type Definitions
//
typedef RS485_ERROR RS485_SYSTEM_RESULT;
typedef RS485_ERROR RS485_USER_RESULT;
typedef RS485_ERROR RS485_DATA_RESULT;
typedef RS485_ERROR RS485_MODEL_RESULT;
typedef RS485_ERROR RS485_COMMUNICATION_RESULT;
typedef RS485_ERROR RS485_BUFFER_RESULT;
typedef RS485_ERROR RS485_CONFIGURATION_RESULT;
typedef RS485_ERROR RS485_HARDWARE_RESULT;
typedef RS485_ERROR RS485_DRIVER_RESULT;
typedef RS485_ERROR RS485_APPLICATION_RESULT;

// Additional result types for interface
typedef RS485_ERROR RS485_CONNECTION_RESULT;
typedef RS485_ERROR RS485_ENUMERATION_RESULT;
typedef RS485_ERROR RS485_DETECTION_RESULT;
typedef RS485_ERROR RS485_REQUEST_RESULT;
typedef RS485_ERROR RS485_VERIFICATION_RESULT;
typedef RS485_ERROR RS485_MODEL_DATA_RESULT;
typedef RS485_ERROR RS485_PORT_RESULT;
typedef RS485_ERROR RS485_PERFORMANCE_RESULT;
typedef RS485_ERROR RS485_CONFIG_RESULT;
typedef RS485_ERROR RS485_LINE_RESULT;

// Convenience constants for common error values
#define ConfigurationResultInvalidValue RS485_ERROR_INVALID_DATA_FORMAT
#define ConnectionResultSuccess RS485_SUCCESS
#define ConnectionResultFailed RS485_ERROR_COMMUNICATION_FAILED

//
// Error Handling Macros
//
#define RS485_SUCCEEDED(error) ((error) == RS485_SUCCESS)
#define RS485_FAILED(error) ((error) != RS485_SUCCESS)

#define RS485_IS_COMMUNICATION_ERROR(error) \
    ((error) >= RS485_ERROR_COMMUNICATION_FAILED && (error) < RS485_ERROR_BUFFER_OVERFLOW)

#define RS485_IS_BUFFER_ERROR(error) \
    ((error) >= RS485_ERROR_BUFFER_OVERFLOW && (error) < RS485_ERROR_INVALID_BAUD_RATE)

#define RS485_IS_CONFIGURATION_ERROR(error) \
    ((error) >= RS485_ERROR_INVALID_BAUD_RATE && (error) < RS485_ERROR_HARDWARE_FAILURE)

#define RS485_IS_HARDWARE_ERROR(error) \
    ((error) >= RS485_ERROR_HARDWARE_FAILURE && (error) < RS485_ERROR_DRIVER_NOT_LOADED)

#define RS485_IS_DRIVER_ERROR(error) \
    ((error) >= RS485_ERROR_DRIVER_NOT_LOADED && (error) < RS485_ERROR_INITIALIZATION_FAILED)

#define RS485_IS_APPLICATION_ERROR(error) \
    ((error) >= RS485_ERROR_INITIALIZATION_FAILED && (error) <= RS485_ERROR_MAX)

//
// Error Utility Functions
//
const CHAR* RS485GetErrorString(_In_ RS485_ERROR Error);
RS485_ERROR_SEVERITY RS485GetErrorSeverity(_In_ RS485_ERROR Error);
VOID RS485InitializeErrorStatistics(_Out_ PRS485_ERROR_STATISTICS Statistics);
VOID RS485UpdateErrorStatistics(_Inout_ PRS485_ERROR_STATISTICS Statistics, _In_ RS485_ERROR Error);
VOID RS485GetErrorStatistics(_In_ const RS485_ERROR_STATISTICS* Statistics, 
                            _Out_ PRS485_ERROR_STATISTICS OutputStatistics);

#endif // RS485_INTERFACE_ERRORS_H
